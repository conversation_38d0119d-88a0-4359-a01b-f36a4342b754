<template>
    <el-container>
        <el-main
            class="nopadding"
            v-loading="loading"
        >
            <scTable
                ref="table"
                :data="list"
                row-key="id"
                @selection-change="selectionChange"
                stripe
                border
                hideDo
                :bottomSlot="true"
                :header-cell-style="{ background: '#EBEEF5' }"
            >
                <el-table-column
                    label="姓名"
                    prop="operName"
                    min-width="100"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="操作时间"
                    prop="operTime"
                    min-width="200"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    label="操作对象"
                    prop="serviceName"
                    min-width="100"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="操作类型"
                    prop="content"
                    min-width="220"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="详情"
                    prop="desc"
                    min-width="220"
                    align="center"
                >
                    <template #default="scope">
                        {{ scope.row.desc }}{{ scope.row.contents ? scope.row.contents : '' }}
                    </template>
                </el-table-column>
                <template #pagination>
                    <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage4"
                        :page-sizes="[10, 20, 30, 40]"
                        :page-size="query.per_page"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="total"
                        small
                    >
                    </el-pagination>
                </template>
                <template #button>
                    <el-button
                        @click="refresh"
                        icon="el-icon-refresh"
                        circle
                        style="margin-left: 15px"
                    ></el-button>
                </template>
            </scTable>
        </el-main>
    </el-container>
</template>

<script>
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
export default {
    name: 'listCrud',
    components: {},
    data() {
        return {
            savetitle: '',
            dialog: {
                save: false,
                info: false,
                add: false
            },
            list: [],
            selection: [],
            query: {
                page: 1,
                per_page: 10
            },
            total: 0,
            loading: false
        }
    },
    created() {
        this.list_post()
    },
    methods: {
        // list列表
        list_post() {
            this.loading = true
            this.$HTTP
                .post('http://assetapi.qixuw.com/oper_log/get_ls', this.query)
                .then((res) => {
                    this.list = res.result.data
                    this.total = res.result.total
                    this.list.forEach((item) => {
                        let desc = `【${item.title}${item.modelName ? '-' : ''}${
                            item.modelName ? item.modelName : ''
                        }】`
                        if (item.content.substring(0, 2) == '修改') {
                            console.log(item.requestParam)
                            let requestParam = JSON.parse(item.requestParam)
                            if (requestParam.modifyData) {
                                let contents = ''
                                requestParam.modifyData.forEach((el) => {
                                    contents =
                                        contents +
                                        el.fieldName +
                                        ' 由"' +
                                        el.oldValue +
                                        '" 变更为 "' +
                                        el.newValue +
                                        '";'
                                })
                                item.contents = contents
                            }
                        }
                        item.desc = desc
                        //     let userNames = JSON.parse(item.userlst).users
                        //     let userName = ''
                        //     for (let i in userNames) {
                        //         // console.log(i==names.length-1);
                        //         if (i == userNames.length - 1) {
                        //             // console.log("最后一个");
                        //             userName = userName + userNames[i].name
                        //         } else {
                        //             userName = userName + userNames[i].name + '、'
                        //         }
                        //     }
                        //     item.userNames = userName
                        //     let actions = JSON.parse(item.actions)
                        //     let action = ''
                        //     for (let i in actions) {
                        //         // console.log(i==names.length-1);
                        //         if (i == actions.length - 1) {
                        //             // console.log("最后一个");
                        //             action = action + actions[i].title
                        //         } else {
                        //             action = action + actions[i].title + '、'
                        //         }
                        //     }
                        //     item.actionTiles = action
                    })
                    this.loading = false
                })
                .finally(() => {})
        },
        // 分页事件
        handleSizeChange(val) {
            console.log(`每页 ${val} 条`)
            this.query.per_page = val
            this.list_post()
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`)
            this.query.page = val
            this.list_post()
        },
        // 刷新
        refresh() {
            this.query.per_page = 10
            this.query.page = 1
            this.list_post()
        }
    }
}
</script>

<style>
.btn {
    border: none;
    color: #409eff;
    background: none;
}

.nopadding {
    position: relative;
}

.scTable-page {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 50px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
    background-color: #fff;
}
.scTable-do {
    white-space: nowrap;
}
.scTable:deep(.el-table__footer) .cell {
    font-weight: bold;
}
.scTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-horizontal {
    height: 12px;
    border-radius: 12px;
}
.scTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-vertical {
    width: 12px;
    border-radius: 12px;
}
.button_lst {
    display: flex;
    justify-content: space-between;
}
.button_left {
    display: flex;
}
.button_left_div {
    margin-right: 15px;
}
</style>
