<template>
    <yp_form
        :columns="columns"
        :backrouter="backrouter"
    ></yp_form>
</template>

<script>
export default {
    name: 'task_group_edit',
    data() {
        return {
            backrouter: '/basic/task_group',
            columns: [
                {
                    label: '分组名称',
                    name: 'title',
                    component: 'input',
                    options: {
                        placeholder: '请输入'
                    },
                    rules: [
                        {
                            required: true,
                            message: '请设置分组名称',
                            trigger: 'change'
                        }
                    ]
                }
            ]
        }
    }
}
</script>

<style>
</style>
