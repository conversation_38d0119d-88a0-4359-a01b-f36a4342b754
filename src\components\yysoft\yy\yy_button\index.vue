<!--
 * @Descripttion: 文件导出
 * @version: 1.1
 * @Author: sakuya
 * @Date: 2022年5月24日16:20:12
 * @LastEditors: 祁强 <EMAIL>
 * @LastEditTime: 2025-03-17 17:35:03
-->

<template>
    <el-button
        :type="type"
        @click="onclick"
        :icon="list"
        :loading="loading"
        >{{ label }}</el-button
    >
</template>

<script>
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
export default {
    name: 'yy_button',
    emits: ['finishEvent', 'dialog'],
    props: {
        label: { type: String, default: '' },
        type: { type: String, default: 'primary' },
        component: { type: String, default: '' },
        options: { type: Object, default: () => null },
        value: { type: Object, default: () => null },
        queryform: { type: Object, default: () => {} }
        // multiple: { type: Boolean, default: false },
        // placeholder: { type: String, default: "" },
        // remote: { type: Object, default: () => null },
        // items: { type: Object, default: () => null },
    },
    data() {
        return {
            loading: false,
            // 跳转传送数据
            transferData: {},
            list: '',
            locklabel: '锁定',
            lockmessage: '是否确认锁定'
            // postData: {},
        }
    },
    mounted() {
        if (this.options) {
            this.list = this.options.icon
        }
    },
    methods: {
        onclick() {
            console.log('99999999', this.queryform)
            let islock = false
            if (this.options.relate) {
                this.options.relate.forEach((item) => {
                    if (this.value[item.prop] == item.value) {
                        islock = true
                        return ElMessage(item.message)
                    }
                })
            }
            if (!islock) {
                this.loading = true
                if (this.component == 'confirm') {
                    // let userlst
                    // this.queryform.userlst && (userlst = JSON.parse(this.queryform.userlst))
                    // let depCount = userlst ? userlst.departments.length : 0
                    // let userCount = userlst ? userlst.users.length : 0
                    // ElMessageBox.confirm(
                    //     userlst && depCount + userCount
                    //         ? `已选择${depCount}个部门和${userCount}个人员,是否执行此操作?`
                    //         : `已选择全部人员,是否执行此操作?`,
                    //     this.options.label ?? '操作预警',
                    //     {
                    //         confirmButtonText: '确定',
                    //         cancelButtonText: '取消',
                    //         type: 'warning'
                    //     }
                    // )
                    ElMessageBox.confirm(this.options.message ?? '是否执行此操作?', this.options.label ?? '操作预警', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    })
                        .then((res) => {
                            if (res === 'confirm') {
                                // this.onsubmit(userlst, depCount, userCount)
                                this.onsubmit()
                            }
                        })
                        .catch((err) => {
                            this.loading = false
                        })
                } else if (this.component == 'form') {
                    if (this.options.remote.state == 'open_door') {
                        this.openDoor()
                    }
                    // 关闭 loading
                    this.loading = false
                    // 处理数据
                    var postData = {}
                    var dataInfo = {}
                    if (this.options.remote) {
                        var rs = this.options.remote.data
                        if (this.options.remote.state == 'add') {
                            dataInfo = this.deepClone(this.options)
                            for (let i in dataInfo) {
                                if (typeof dataInfo[i] == 'object') {
                                    dataInfo[i] = JSON.stringify(dataInfo[i])
                                }
                            }
                            // 跳转
                            this.$router.push({
                                name: this.options.name,
                                params: {
                                    ...dataInfo
                                }
                            })
                        }
                        for (const key in rs) {
                            if (rs[key].substring(0, 1) == '$') {
                                postData[key] = this.value[rs[key].substring(1)]
                                if (postData[key] == '') return
                            } else {
                                postData[key] = rs[key]
                            }
                        }
                        dataInfo = this.deepClone(this.options)
                        dataInfo.remote.data = postData
                        for (let i in dataInfo) {
                            if (typeof dataInfo[i] == 'object') {
                                dataInfo[i] = JSON.stringify(dataInfo[i])
                            }
                        }
                        console.log('yy_button 走了么', dataInfo)
                        // 跳转
                        this.$router.push({
                            name: this.options.name,
                            params: {
                                ...dataInfo
                            }
                        })
                    }
                } else if (this.component == 'lock') {
                    this.loading = true
                    ElMessageBox.confirm(this.lockmessage ?? '是否执行此操作?', this.locklabel ?? '操作预警', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    })
                        .then(() => {
                            this.options.remote.data.forEach((el) => {
                                if (typeof el == 'string') {
                                    console.log('string')
                                } else if (Array.isArray(el)) {
                                    console.log('array')
                                } else if (typeof el == 'object') {
                                    let postlock = {}
                                    for (let key in el) {
                                        if (el[key].substring(0, 1) == '$') {
                                            postlock[key] = this.value[el[key].substring(1)]
                                        } else {
                                            postlock[key] = el[key]
                                        }
                                    }
                                    postlock.lock == 0
                                        ? ((postlock.lock = 1),
                                          (this.locklabel = '解锁'),
                                          (this.lockmessage = '是否执行解锁'))
                                        : ((postlock.lock = 0),
                                          (this.locklabel = '锁定'),
                                          (this.lockmessage = '是否执行锁定'))
                                    this.$HTTP.post(this.options.remote.api, postlock).then((res) => {
                                        if (res.errcode != 0) {
                                            ElMessage.error(res.errmsg)
                                        } else {
                                            ElMessage.success('操作成功!')
                                            this.$emit('finishEvent', true)
                                        }
                                        this.oncancel()
                                    })
                                } else {
                                    console.log('没有处理key_map的类型-------->', typeof el)
                                }
                            })
                        })
                        .catch(() => {
                            this.loading = false
                            this.$emit('finishEvent', true)
                        })
                } else if (this.component == 'dialog') {
                    // console.log('dialog')
                    this.loading = false
                    var rs = this.options.remote.data
                    var postData = {}
                    var dataInfo = {}
                    for (const key in rs) {
                        if (rs[key].substring(0, 1) == '$') {
                            postData[key] = this.value[rs[key].substring(1)]
                            if (postData[key] == '') return
                        } else {
                            postData[key] = rs[key]
                        }
                    }
                    dataInfo = this.deepClone(this.options)
                    dataInfo.remote.data = postData
                    // console.log('----------------------------------')
                    // console.log(dataInfo.remote.data)
                    // console.log(this.value)
                    this.$emit('dialog', dataInfo)
                } else if (this.component == 'list') {
                    // 关闭 loading
                    this.loading = false
                    var postData = {}
                    var rs = this.options.remote.data
                    var frs = this.options.remote.queryformdata
                    for (const key in rs) {
                        if (rs[key].substring(0, 1) == '$') {
                            postData[key] = this.value[rs[key].substring(1)]
                            if (postData[key] == '') return
                        } else {
                            postData[key] = rs[key]
                        }
                    }
                    for (const key in frs) {
                        if (frs[key].substring(0, 1) == '$') {
                            postData[key] = this.queryform[frs[key].substring(1)]
                            // if (postData[key] == '') return
                        } else {
                            postData[key] = frs[key]
                        }
                    }
                    // 跳转
                    this.$router.push({
                        name: this.options.name,
                        params: {
                            ...postData
                        }
                    })
                } else if (this.component == 'click') {
                    // console.log('无操作');
                    this.clickdef()
                } else if (this.component == 'switchState') {
                    // console.log(this.value)
                    // console.log(this.options)
                    ElMessageBox.confirm(this.options.message ?? '是否执行此操作?', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    })
                        .then(() => {
                            let postlock = {}
                            var el = this.options.remote.postdata
                            for (let key in el) {
                                if (el[key].substring(0, 1) == '$') {
                                    postlock[key] = this.value[el[key].substring(1)]
                                } else {
                                    postlock[key] = el[key]
                                }
                            }
                            // console.log(this.options.name)
                            postlock[this.options.name] = postlock[this.options.name] == 0 ? 1 : 0
                            this.$HTTP.post(this.options.remote.api, postlock).then((res) => {
                                if (res.errcode != 0) {
                                    ElMessage.error(res.errmsg)
                                } else {
                                    ElMessage.success('操作成功!')
                                    this.$emit('finishEvent', true)
                                }
                                this.oncancel()
                            })
                        })
                        .catch(() => {
                            this.loading = false
                        })
                }
            }
        },
        oncancel() {
            if (this.component == 'confirm') {
                this.confirm = false
            }
            this.loading = false
        },
        onsubmit(userlst, depCount, userCount) {
            var postData = {}
            if (this.options.remote) {
                var rs = this.options.remote.data
                for (const key in rs) {
                    if (rs[key].substring && rs[key].substring(0, 1) == '$') {
                        postData[key] = this.value[rs[key].substring(1)]
                        if (postData[key] == '') return
                    } else {
                        postData[key] = rs[key]
                    }
                }
                if (userlst && depCount + userCount) {
                    postData.userlst = this.queryform.userlst
                    postData.all_user = 0
                } else {
                    // postData.all_user = 1
                }
                this.$HTTP.get(this.options.remote.api, postData).then((res) => {
                    if (res.errcode != 0) {
                        ElMessage.error(res.errmsg)
                    } else {
                        ElMessage.success('操作成功!')
                        this.$emit('finishEvent', true)
                    }
                    this.oncancel()
                })
            }
        },
        clickdef() {
            var postData = {}
            if (this.options.remote) {
                var rs = this.options.remote.data
                for (const key in rs) {
                    if (rs[key].substring(0, 1) == '$') {
                        postData[key] = this.value[rs[key].substring(1)]
                        // if (postData[key] == '') return
                    } else {
                        postData[key] = rs[key]
                    }
                }
                for (const key in postData) {
                    if (key != 'id') {
                        postData[key] = 1
                    }
                }
                this.$HTTP.get(this.options.remote.api, postData).then((res) => {
                    if (res.errcode != 0) {
                        ElMessage.error(res.errmsg)
                    } else {
                        ElMessage.success('操作成功!')
                        this.$emit('finishEvent', true)
                    }
                    this.oncancel()
                })
            }
        },
        // 深拷贝
        deepClone(obj) {
            //判断拷贝的要进行深拷贝的是数组还是对象，是数组的话进行数组拷贝，对象的话进行对象拷贝
            var objClone = Array.isArray(obj) ? [] : {}
            //进行深拷贝的不能为空，并且是对象或者是
            if (obj && typeof obj === 'object') {
                for (let key in obj) {
                    if (obj.hasOwnProperty(key)) {
                        if (obj[key] && typeof obj[key] === 'object') {
                            objClone[key] = this.deepClone(obj[key])
                        } else {
                            objClone[key] = obj[key]
                        }
                    }
                }
            }
            return objClone
        },
        // 开门
        openDoor() {
            this.loading = true
            ElMessageBox.confirm(this.options.message ?? '是否执行此操作?', this.options.label ?? '操作预警', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    var postData = {}
                    var rs = this.options.remote.data
                    for (const key in rs) {
                        if (rs[key].substring(0, 1) == '$') {
                            postData[key] = this.value[rs[key].substring(1)]
                            if (postData[key] == '') return
                        } else {
                            postData[key] = rs[key]
                        }
                    }
                    this.$HTTP.post(this.options.remote.api, postData).then((res) => {
                        ElNotification({
                            title: '成功',
                            message: '开门操作成功！',
                            type: 'success'
                        })
                    })
                })
                .catch(() => {
                    this.loading = false
                })
        }
        //处理远程选项数据
        // getData(e) {
        // 	if (e) {
        // 		if (this.remote) {
        // 			this.loading = true;
        // 			var postData = {};
        // 			var rs = this.remote.data;
        // 			for (const key in rs) {
        // 				if (rs[key].substring(0, 1) == "$") {
        // 					postData[key] = this.form[rs[key].substring(1)];
        // 					console.log(postData[key]);
        // 					if (postData[key] == "") return;
        // 				} else {
        // 					postData[key] = rs[key];
        // 				}
        // 			}
        // 			this.$HTTP
        // 				.get(this.remote.api, postData)
        // 				.then((res) => {
        // 					this.remoteitems = res.result;
        // 					this.loading = false;
        // 				});
        // 		}
        // 	}
        // },
    }
}
</script>

<style></style>
