<template>
    <yy_workflow
        v-model="data.nodeConfig"
        :isRecommend="true"
        :recommendOptions.sync="recommendOptions"
    ></yy_workflow>
</template>

<script>
export default {
    name: 'workflow',
    data() {
        return {
            data: {
                // id: 1,
                name: '任务流',
                nodeConfig: {
                    nodeName: '任务开启',
                    type: 0,
                    nodeRoleList: [],
                    childNode: {}
                }
            }
        }
    },
    mounted() {
        if (this.postData.name) {
            this.data = this.postData
        }
    },
    watch: {
        postData(newVal, oldVal) {
            // console.log('1111111111', newVal)
            this.data = newVal
            // do something
        }
    },
    props: {
        postData: { type: Object, default: () => {} },
        recommendOptions: { type: Object, default: () => {} }
        // id: { type: Number, default: 0 }
    },
    methods: {
        exportJson() {
            // this.$message('返回值请查看F12控制台console.log()')
            console.log(this.data)
        }
    }
}
</script>

<style></style>
