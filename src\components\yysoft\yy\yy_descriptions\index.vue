<template>
    <el-button
        type="primary"
        plain
        size="small"
        @click="openDrawer"
    >
        {{ label }}
    </el-button>

    <el-drawer
        calss="el-drawer"
        v-model="drawer"
        append-to-body
        :title="title"
        :direction="this.direction"
        :before-close="handleClose"
        size="40%"
    >
        <el-descriptions
            :direction="directionDesc"
            :column="column"
            border
            class="el-descriptions"
        >
            <template
                v-for="(item, index) in items"
                :key="index"
            >
                <el-descriptions-item
                    :label="item.label"
                    v-if="!item.dynamicShow"
                >
                    <descriptionsItem
                        :form="formData[item.prop]"
                        :formAll="formData"
                        :item="item"
                        :component="item.component"
                        :options="item.options"
                    ></descriptionsItem>
                </el-descriptions-item>
            </template>
        </el-descriptions>
        <slot name="footer"></slot>
        <div
            v-for="item in buttonList"
            :key="item"
        >
            <yybutton
                v-if="formData[item.type] != 1"
                :formAll="formData"
                :label="item.label"
                :component="item.component"
                :options="item.options"
                @finishEvent="finishEvent"
            >
            </yybutton>
        </div>
    </el-drawer>
</template>

<script>
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import descriptionsItem from './Items.vue'
import yybutton from './yybutton.vue'
export default {
    name: '',
    components: {
        descriptionsItem,
        yybutton
    },
    props: {
        label: { type: String, default: '' },
        title: { type: String, default: '' },
        direction: { type: String, default: 'rtl' },
        directionDesc: { type: String, default: 'horizontal' },
        column: { type: Number, default: 1 },
        form: { type: Object, default: () => {} },
        remote: { type: Object, default: () => {} },
        items: { type: Array, default: () => [] },
        buttonList: { type: Array, default: () => [] },
        haveslot: { type: Boolean, default: false }
    },
    emits: ['infoData', 'finish-event'],
    data() {
        return {
            drawer: false,
            formData: {}
        }
    },
    mounted() {
        // this.getData()
    },
    methods: {
        finishEvent() {
            // this.getData()
            // console.log('-----')
            this.$emit('finish-event', true)
            this.getData()
        },
        openDrawer() {
            this.drawer = true
            this.getData()
        },
        // 获取 info 数据
        getData() {
            var postData = {}
            var rs = this.remote.data
            for (const key in rs) {
                if (rs[key].substring(0, 1) == '$') {
                    postData[key] = this.form[rs[key].substring(1)]
                    if (postData[key] == '') return
                } else {
                    postData[key] = rs[key]
                }
            }
            this.$HTTP.get(this.remote.api, postData).then((res) => {
                if (res.errcode != 0) {
                    ElMessage.error(res.errmsg)
                } else {
                    // console.log('------410-------')
                    // console.log(res)
                    this.formData = res.result
                    if (this.haveslot) {
                        this.$emit('infoData', JSON.stringify(res.result))
                    }
                    this.handleForm()
                    this.handleSelect()
                }
            })
        },
        // 处理远程数据请求
        handleSelect() {
            this.items.forEach((el) => {
                if (el.component == 'remoteselect') {
                    this.$HTTP.get(el.options.remote.api).then((res) => {
                        if (res.errcode != 0) {
                            ElMessage.error(res.errmsg)
                        } else {
                            // console.log('------420-------')
                            // console.log(res)
                            res.result.forEach((k) => {
                                if (k[el.options.remote.value] == this.formData[el.prop]) {
                                    this.formData[el.prop] = k[el.options.remote.label]
                                }
                            })
                        }
                    })
                }
            })
        },
        // 根据form展示数据
        handleForm() {
            if (this.items) {
                this.items.forEach((el) => {
                    // console.log('--------------------420------------------');
                    // console.log(this.form[el.options.dynamicShowValue]);
                    var isExecute = typeof el.dynamicShow
                    if (isExecute == 'boolean') {
                        // console.log('动态处理')
                        if (typeof el.options.dynamicShowValue == 'number') {
                            if (el.options.dynamicShowValue == this.formData[el.options.dynamicPorp]) {
                                // console.log('--1能显示么--')
                                el.dynamicShow = false
                            } else {
                                // console.log('--1能隐藏么--')
                                el.dynamicShow = true
                            }
                        }
                        if (typeof el.options.dynamicHideValue == 'number') {
                            if (el.options.dynamicHideValue == this.formData[el.options.dynamicPorp]) {
                                // console.log('--2能隐藏么--')
                                el.dynamicShow = true
                            } else {
                                // console.log('--2能显示么--')
                                el.dynamicShow = false
                            }
                        }
                    }
                })
            }
        }
    }
}
</script>

<style scoped lang="scss">
.el-drawer {
    width: 100%;
    .el-descriptions {
        padding: 0 10px;
        // width: 80%;
    }
}
</style>