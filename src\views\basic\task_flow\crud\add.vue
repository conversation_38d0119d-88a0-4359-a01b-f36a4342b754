<template>
    <el-container>
        <el-header>
            <h2>新增</h2>
            <el-button
                @click="exportJson"
                :loading="loading"
                >提交</el-button
            >
        </el-header>
        <el-main>
            <yy_workflow v-model="data.nodeConfig"></yy_workflow>
        </el-main>
    </el-container>
</template>

<script>
import { ElMessage } from 'element-plus'
import useTabs from '@/utils/useTabs'
export default {
    name: 'workflow',
    data() {
        return {
            data: {
                // id: 1,
                name: '任务流',
                nodeConfig: {
                    // nodeName: '任务开启',
                    type: 0,
                    // nodeRoleList: [],
                    childNode: {}
                }
            },
            loading: false
        }
    },
    mounted() {},
    methods: {
        exportJson() {
            this.loading = true
            let process = JSON.parse(JSON.stringify(this.data))
            console.log('获取结果 --->', this.data)
            this.handleJson(process.nodeConfig.childNode)
            this.$HTTP
                .post('flow/post_add', {
                    title: this.data.nodeConfig.title,
                    process: JSON.stringify(process)
                })
                .then((res) => {
                    // console.log('获取结果 --->', res)
                    if (res.errcode == 0) {
                        // console.log('获取任务分组1 --->', res.result)
                        // this.taskGroupList = res.result
                        ElMessage.success('提交成功')
                        useTabs.close()
                    } else {
                        ElMessage.error(res.errmsg)
                    }
                    this.loading = false
                })
                .finally(() => {
                    this.loading = false
                })
        },
        // 处理json
        handleJson(obj) {
            if (obj.userLst) {
                obj.userLst = JSON.parse(obj.userLst)
            }
            for (let i in obj) {
                // console.log('22222222222222222', i)
                if (i == 'conditionNodes') {
                    obj[i].forEach((el) => {
                        if (el.userLst) {
                            el.userLst = JSON.parse(el.userLst)
                        }
                    })
                } else if (i == 'childNode') {
                    if (obj[i].type) {
                        if (obj[i].userLst) {
                            obj[i].userLst = JSON.parse(obj[i].userLst)
                            if (obj[i].childNode.type) {
                                this.handleJson(obj)
                            }
                        }
                    }
                }
            }
        }
    }
}
</script>

<style></style>
