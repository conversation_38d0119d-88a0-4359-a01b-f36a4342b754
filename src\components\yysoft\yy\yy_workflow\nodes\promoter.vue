<template>
    <div class="node-wrap">
        <div
            class="node-wrap-box start-node"
            @click="show"
        >
            <div
                class="title"
                style="background: #576a95"
            >
                <span>任务流名称</span>
            </div>
            <div class="content">
                <span>{{ toText(nodeConfig) }}</span>
            </div>
        </div>
        <add-node v-model="nodeConfig.childNode"></add-node>
        <el-drawer
            title="发起人"
            v-model="drawer"
            destroy-on-close
            append-to-body
            :size="500"
        >
            <template #header>
                <div class="node-wrap-drawer__title">
                    <label>任务流</label>
                </div>
            </template>
            <el-container>
                <el-main style="padding: 0 20px 20px 20px">
                    <el-form label-position="top">
                        <el-form-item label="任务流名称">
                            <el-input
                                v-model="form.title"
                                placeholder="请输入"
                            />
                        </el-form-item>
                    </el-form>
                </el-main>
                <el-footer>
                    <el-button
                        type="primary"
                        @click="save"
                        >保存</el-button
                    >
                    <el-button @click="drawer = false">取消</el-button>
                </el-footer>
            </el-container>
        </el-drawer>
    </div>
</template>

<script>
import addNode from './addNode'

export default {
    inject: ['select'],
    props: {
        modelValue: { type: Object, default: () => {} }
    },
    components: {
        addNode
    },
    data() {
        return {
            nodeConfig: {},
            drawer: false,
            isEditTitle: false,
            form: {}
            // title: ''
        }
    },
    watch: {
        modelValue() {
            this.nodeConfig = this.modelValue
        }
    },
    mounted() {
        this.nodeConfig = this.modelValue
    },
    methods: {
        show() {
            this.form = {}
            this.form = JSON.parse(JSON.stringify(this.nodeConfig))
            this.isEditTitle = false
            this.drawer = true
        },
        // editTitle() {
        //     this.isEditTitle = true
        //     this.$nextTick(() => {
        //         this.$refs.nodeTitle.focus()
        //     })
        // },
        // saveTitle() {
        //     this.isEditTitle = false
        // },
        // selectHandle(type, data) {
        //     this.select(type, data)
        // },
        // delRole(index) {
        //     this.form.nodeRoleList.splice(index, 1)
        // },
        save() {
            this.$emit('update:modelValue', this.form)
            this.drawer = false
        },
        toText(nodeConfig) {
            console.log('nodeConfig --->', nodeConfig)
            if (nodeConfig.title && nodeConfig.title.length > 0) {
                return nodeConfig.title
            } else {
                return '请输入'
            }
        }
    }
}
</script>

<style>
</style>
