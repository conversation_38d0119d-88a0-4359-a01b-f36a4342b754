export function ISjurisdiction(name, itemName) {
    let datalist = localStorage.getItem('PERMISSION')
    if (!datalist) {
        return false
    }

    try {
        let data = JSON.parse(datalist)

        // 使用 for...of 循环替代 forEach，这样可以使用 return
        for (let item of data.content.actions) {
            if (item.title === name) {
                if (item.postData && item.postData.length > 0) {
                    for (let v of item.postData) {
                        console.log(v, 888)
                        if (v.title === itemName) {
                            return true  // 找到匹配项，返回 true
                        }
                    }
                }
            }
        }

        // 没有找到匹配的 name
        return false

    } catch (error) {
        console.error('Error parsing PERMISSION data:', error)
        return false
    }
}