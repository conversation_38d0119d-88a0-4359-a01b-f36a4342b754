<template>
    <el-tabs
        v-model="activeName"
        @tab-click="handleClick"
        type="card"
    >
        <template
            v-for="(item, index) in tabitems"
            :key="index"
        >
            <el-tab-pane
                :label="item.label"
                :name="item.name"
            >
                <template v-if="item.options.valuetype == 'queryform'">
                    <yy_queryform
                        ref="queryform"
                        :filed="item.options.formitems"
                        v-model="queryform"
                        @weekdata="weekdata"
                    >
                        <div class="button_lst">
                            <div class="button_left">
                                <div
                                    v-for="(item, index) in buttonList"
                                    :key="index"
                                    class="button_left_div"
                                >
                                    <yy_upload
                                        v-if="item.component == 'upload'"
                                        :label="item.label"
                                        :templateUrl="item.templateUrl"
                                        :url="item.url"
                                        :maxSize="item.maxSize"
                                    ></yy_upload>
                                    <yy_button
                                        v-if="item.component == 'form'"
                                        :label="item.label"
                                        :type="item.type"
                                        :options="item.options"
                                        :value="query"
                                        :component="item.component"
                                    ></yy_button>
                                    <yy_button
                                        v-if="item.component == 'confirm'"
                                        :label="item.label"
                                        :type="item.type"
                                        :options="item.options"
                                        :value="query"
                                        :component="item.component"
                                        @finishEvent="finishEvent"
                                    ></yy_button>
                                </div>
                            </div>
                            <div class="button-right">
                                <el-button
                                    type="primary"
                                    @click="handledata"
                                    icon="el-icon-Search"
                                    >查询</el-button
                                >
                                <el-button
                                    plain
                                    @click="resetForm"
                                    >重置</el-button
                                >
                                <yy_export
                                    v-if="derive"
                                    :url="derive.url"
                                    :fileName="derive.filename"
                                    showData
                                    :column="column"
                                    :fileTypes="['xlsx']"
                                    :dynamicColumns="dynamicColumns"
                                    :query="queryform"
                                    :showsummary="showsummary"
                                    :data="derive.data"
                                ></yy_export>
                            </div>
                        </div>
                    </yy_queryform>
                </template>
            </el-tab-pane>
        </template>
    </el-tabs>
</template>
  <script>
export default {
    props: {
        tabitems: { type: Object, default: () => {} },
        derive: { type: Object, default: () => {} },
        column: { type: Object, default: () => {} }
    },
    emits: ['queryformdata'],
    data() {
        return {
            activeName: 0,
            queryform: {},
            postdata: {},
            weekdataObj: {}
        }
    },
    watch: {
        activeName: {
            handler() {
                this.resetForm()
            },
            immediate: true
        }
    },
    methods: {
        refresh() {
            Object.assign(this.postdata, this.weekdataObj)
            // console.log(this.postdata)
            this.$emit('queryformdata', this.postdata)
        },
        handledata() {
            // console.log('-----handledata---')
            this.tabitems.forEach((item) => {
                if (item.name == this.activeName) {
                    // this.queryform[item.prop] = this.activeName
                    this.postdata[item.prop] = this.activeName
                    var formitems = item.options.formitems
                    formitems.forEach((itm) => {
                        this.postdata[itm.name] = this.queryform[itm.name]
                    })
                    // console.log(item)
                }
            })
            this.refresh()
        },
        resetForm() {
            this.postdata = {}
            this.tabitems.forEach((item) => {
                if (item.name == this.activeName) {
                    this.postdata[item.prop] = this.activeName
                    var formitems = item.options.formitems
                    formitems.forEach((itm) => {
                        // console.log(itm.value)
                        // if (itm.value) {
                        this.postdata[itm.name] = itm.value
                        // }
                    })
                    this.queryform = this.postdata
                    // console.log('------320-------')
                    // console.log(this.postdata)
                    // console.log(this.queryform)
                }
            })
            // console.log('走了么')
            // console.log(this.postdata)
            this.refresh()
        },
        weekdata(date) {
            this.weekdataObj = date
        }
    }
}
</script>

<style scoped>
.button_lst {
    display: flex;
    justify-content: space-between;
}
.button_left {
    display: flex;
}
.button_left_div {
    margin-right: 15px;
}
</style>
