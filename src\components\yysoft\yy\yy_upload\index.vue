<!--
 * @Descripttion: 文件导入
 * @version: 1.0
 * @Author: sakuya
 * @Date: 2022年5月24日11:30:03
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-01-06 20:56:45
-->

<template>
    <div>
        <el-button
            type="primary"
            icon="sc-icon-upload"
            @click="open"
            >{{ label }}</el-button
        >
    </div>
    <el-dialog
        v-model="dialog"
        title="导入"
        :width="550"
        :close-on-click-modal="false"
        append-to-body
        destroy-on-close
    >
        <el-progress
            v-if="loading"
            :text-inside="true"
            :stroke-width="20"
            :percentage="percentage"
            style="margin-bottom: 15px"
        />
        <div v-loading="loading">
            <el-upload
                ref="uploader"
                drag
                :accept="accept"
                :maxSize="maxSize"
                :limit="1"
                :data="data"
                :show-file-list="false"
                :multiple="multiple"
                :http-request="request"
                :before-upload="before"
                :on-progress="progress"
                :on-success="success"
                :on-error="error"
            >
                <slot name="uploader">
                    <el-icon class="el-icon--upload"><el-icon-upload-filled /></el-icon>
                    <div class="el-upload__text">将文件拖到此处或 <em>点击选择文件上传</em></div>
                </slot>
                <template #tip>
                    <div class="el-upload__tip">
                        <template v-if="tip">{{ tip }}</template>
                        <template v-else>请上传小于或等于 {{ maxSize }}M 的 {{ accept }} 格式文件</template>
                        <p
                            v-if="templateUrl || columnData"
                            style="margin-top: 7px"
                        >
                            <el-link
                                target="_blank"
                                type="primary"
                                :underline="false"
                                @click="downloadFile"
                                >下载导入模板</el-link
                            >
                        </p>
                    </div>
                </template>
            </el-upload>
            <el-form
                v-if="$slots.form"
                inline
                label-width="100px"
                label-position="left"
                style="margin-top: 18px"
            >
                <slot
                    name="form"
                    :formData="formData"
                ></slot>
            </el-form>
        </div>
        <template #footer>
            <!-- <span class="dialog-footer">
        <el-button @click="centerDialogVisible = false">Cancel</el-button>
        <el-button type="primary" @click="centerDialogVisible = false">
          Confirm
        </el-button>
      </span> -->
        </template>
    </el-dialog>
</template>

<script>
import { ElMessage } from 'element-plus'
export default {
    name: 'yy_upload',
    emits: ['success'],
    props: {
        templateUrl: { type: String, default: '' },
        label: { type: String, default: '上传' },
        url: { type: String, default: '' },
        data: { type: Object, default: () => {} },
        value: { type: Object, default: () => null },
        downUrl: { type: String, default: '' },
        columnData: { type: Object, default: () => {} },
        filename: { type: String, default: '导入模版' },
        accept: { type: String, default: '.xls, .xlsx' },
        maxSize: { type: Number, default: 10 },
        multiple: { type: Boolean, default: false },
        tip: { type: String, default: '' }
    },
    data() {
        return {
            dialog: false,
            loading: false,
            percentage: 0,
            formData: {}
        }
    },
    mounted(){
    },
    methods: {
        open() {
            this.dialog = true
            this.formData = {}
        },
        close() {
            this.dialog = false
        },
        before(file) {
            const maxSize = file.size / 1024 / 1024 < this.maxSize
            if (!maxSize) {
                this.$message.warning(`上传文件大小不能超过 ${this.maxSize}MB!`)
                return false
            }
            this.loading = true
        },
        progress(e) {
            this.percentage = e.percent
        },
        success(res, file) {
            this.$refs.uploader.handleRemove(file)
            this.$refs.uploader.clearFiles()
            this.loading = false
            this.percentage = 0
            this.$emit('success', res, this.close)
        },
        error(err) {
            this.loading = false
            this.percentage = 0
            this.$notify.error({
                title: '上传文件未成功',
                message: err
            })
        },
        request(param) {
            Object.assign(param.data, this.formData)
            const data = new FormData()
            data.append(param.filename, param.file)
            for (const key in param.data) {
                data.append(key, param.data[key])
            }
            var rs = this.data
            for (const key in rs) {
                if (rs[key].substring(0, 1) == '$') {
                    data[key] = this.value[rs[key].substring(1)]
                    console.log(data[key])
                    if (data[key] == '') return
                } else {
                    data[key] = rs[key]
                }
            }
            console.log(data)
            this.$HTTP
                .post(this.url, data, {
                    onUploadProgress: (e) => {
                        const complete = parseInt(((e.loaded / e.total) * 100) | 0, 10)
                        param.onProgress({ percent: complete })
                    }
                })
                .then((res) => {
                    param.onSuccess(res)
                    ElMessage({
                        message: '操作成功!.',
                        type: 'success'
                    })
                    // 添加apk本地存储
                    localStorage.setItem('apk', res.result)
                    this.$emit("upload",res.result);
                    this.dialog = false
                })
                .catch((err) => {
                    param.onError(err)
                })
        },
        downLoadFile(res, filename) {
            const link = document.createElement('a')
            const blob = new Blob([res], {
                type: 'application/vnd.ms-excel'
            })
            link.style.display = 'none'
            link.href = URL.createObjectURL(blob)
            link.download = filename
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
        },
        downloadFile() {
            var that = this
            this.downLoading = true
            console.log(this.filename)
            if (this.templateUrl == '') {
                import('@/utils/Export2Excel').then((excel) => {
                    let columnArr = this.columnData.map((n) => n.label)
                    excel.export_json_to_excel({
                        header: columnArr,
                        data: [],
                        filename: that.filename
                    })
                    this.dialog = false
                })
            } else {
                this.$HTTP
                    .get(this.templateUrl,{},{ responseType: 'blob' })
                    .then((res) => {
                        this.downLoading = false
                        this.downLoadProgress = 0
                        if (res.result) {
                            import('@/utils/Export2Excel').then((excel) => {
                                let columnArr = this.columnData.map((n) => n).join(',')
                                excel.export_json_to_excel({
                                    header: columnArr,
                                    data: res.result,
                                    filename: that.filename
                                })
                                this.dialog = false
                            })
                        } else {
                            this.downLoadFile(res, that.filename)
                        }
                    })
                    .catch((err) => {
                        this.downLoading = false
                        this.downLoadProgress = 0
                        this.$notify.error({
                            title: '下载文件失败',
                            message: err
                        })
                    })
            }
        }
    }
}
</script>

<style></style>
