<template>
    <el-container>
        <el-aside :width="form.type == 1 ? '50%' : '100%'">
            <el-container>
                <el-header>
                    <h2>新增</h2>
                </el-header>
                <el-main>
                    <el-form
                        ref="form"
                        :model="form"
                        label-width="120px"
                        label-position="left"
                    >
                        <el-form-item
                            label="任务名称"
                            prop="title"
                            :rules="[{ required: true, message: '请输入任务名称', trigger: 'blur' }]"
                        >
                            <el-input v-model="form.title"></el-input>
                        </el-form-item>
                        <el-form-item label="任务明细">
                            <el-input
                                type="textarea"
                                v-model="form.content"
                                maxlength="50"
                                show-word-limit
                            ></el-input>
                        </el-form-item>
                        <el-form-item
                            label="任务分组"
                            prop="group_id"
                            :rules="[{ required: true, message: '请选择任务分组', trigger: 'blur' }]"
                        >
                            <el-select
                                v-model="form.group_id"
                                placeholder="请选择"
                            >
                                <template
                                    v-for="(el, i) in taskGroupList"
                                    :key="i"
                                >
                                    <el-option
                                        :label="el.title"
                                        :value="el.id"
                                    ></el-option>
                                </template>
                            </el-select>
                        </el-form-item>
                        <el-form-item
                            label="任务优先级"
                            v-if="!form.immediately"
                            :rules="[{ required: true, message: '请选择任务优先级', trigger: 'blur' }]"
                        >
                            <el-select
                                v-model="form.priority"
                                placeholder="请选择"
                            >
                                <el-option
                                    label="一级"
                                    value="1"
                                ></el-option>
                                <el-option
                                    label="二级"
                                    value="2"
                                ></el-option>
                                <el-option
                                    label="三级"
                                    value="3"
                                ></el-option>
                                <el-option
                                    label="四级"
                                    value="4"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item
                            label="任务流或人员"
                            prop="type"
                            :rules="[{ required: true, message: '请选择任务流或人员', trigger: 'blur' }]"
                        >
                            <el-select
                                v-model="form.type"
                                placeholder="请选择"
                            >
                                <!-- <el-option
                                    label="人员"
                                    value="0"
                                ></el-option>
                                <el-option
                                    label="任务流"
                                    value="1"
                                ></el-option> -->
                                <template
                                    v-for="(el, i) in typeList"
                                    :key="i"
                                >
                                    <el-option
                                        :label="el.title"
                                        :value="el.id"
                                        :disabled="el.id === 1 && !isrenwu"
                                    ></el-option>
                                </template>
                            </el-select>
                        </el-form-item>
                        <template v-if="form.type == 0">
                            <el-form-item>
                                <template #label>
                                    <img
                                        src="/img/second.png"
                                        alt=""
                                        :style="{ width: '35px', height: '35px' }"
                                    />
                                    <span>人员</span>
                                </template>
                                <yy_selectuser v-model="form.userlst"></yy_selectuser>
                            </el-form-item>
                        </template>
                        <template v-else-if="form.type == 1">
                            <el-form-item>
                                <template #label>
                                    <img
                                        src="/img/second.png"
                                        alt=""
                                        :style="{ width: '35px', height: '35px' }"
                                    />
                                    <span>任务流</span>
                                </template>
                                <el-select
                                    v-model="form.flow_id"
                                    placeholder="请选择"
                                    @change="changeFlow"
                                >
                                    <template
                                        v-for="(el, i) in folwList"
                                        :key="i"
                                    >
                                        <el-option
                                            :label="el.title"
                                            :value="el.id"
                                        ></el-option>
                                    </template>
                                </el-select>
                            </el-form-item>
                        </template>
                        <el-form-item label="任务附件">
                            <el-upload
                                class="upload-demo"
                                action="https://fileapi.qixuw.com/file/post_upload"
                                :headers="{
                                    Authorization: token
                                }"
                                :on-remove="handleRemove"
                                :on-success="handleSuccess"
                                :file-list="fileList"
                            >
                                <div class="upload">
                                    <el-button>点击上传</el-button>
                                </div>
                            </el-upload>
                        </el-form-item>
                        <el-form-item label="立即执行">
                            <el-switch v-model="form.immediately" />
                        </el-form-item>
                        <el-form-item label="新建项目群">
                            <el-switch v-model="form.chat" />
                        </el-form-item>
                        <el-form-item>
                            <el-button
                                type="primary"
                                :loading="loading"
                                @click="onSubmit"
                                >提交</el-button
                            >
                            <el-button @click="cancel">取消</el-button>
                        </el-form-item>
                    </el-form>
                </el-main>
            </el-container>
        </el-aside>
        <el-main v-if="form.type == 1">
            <div
                class="flow"
                @click="handleClick"
            >
                <flow
                    :postData="process"
                    :recommendOptions.sync="recommendOptions"
                    ref="flow"
                ></flow>
            </div>
        </el-main>
    </el-container>
</template>

<script>
import axios from 'axios'
import flow from './flow.vue'
import tool from '@/utils/tool'
import { ElMessage } from 'element-plus'
import useTabs from '@/utils/useTabs'
export default {
    data() {
        return {
        isrenwu: false,
            form: {
                priority: '1'
            },
            taskGroupList: [],
            folwList: [],
            fileList: [],
            token: tool.data.get('TOKEN'),
            fileData: [],
            process: {},
            recommendOptions: {},
            typeList: [
                { title: '人员', id: 0 },
                { title: '任务流', id: 1 }
            ],
            loading: false
        }
    },
    components: {
        flow
    },
    methods: {
        onSubmit() {
            // console.log(this.form)
            this.loading = true
            this.$refs['form'].validate((valid) => {
                console.log(valid,9999)
                if (valid) {
                    let postData = {
                        title: this.form.title,
                        content: this.form.content,
                        group_id: this.form.group_id,
                        type: this.form.type,
                        file_data: JSON.stringify(this.fileData),
                        immediately: this.form.immediately ? 1 : 0,
                        chat: this.form.chat ? 1 : 0,
                        priority: this.form.priority
                    }
                    this.taskGroupList.forEach((el) => {
                        if (el.id == this.form.group_id) {
                            postData.group_title = el.title
                        }
                    })
                    if (this.form.type == 0) {
                        postData.userlst = this.form.userlst
                    } else if (this.form.type == 1) {
                        postData.flow_id = this.form.flow_id
                        this.folwList.forEach((el) => {
                            if (el.id == this.form.flow_id) {
                                postData.flow_title = el.title
                            }
                        })
                        let userlst = JSON.parse(JSON.stringify(this.$refs.flow.data))
                        this.handleJson(userlst.nodeConfig.childNode)
                        postData.userlst = JSON.stringify(userlst)
                    }
                    this.$HTTP
                        .post('task/post_add', postData)
                        .then((res) => {
                            // console.log('获取任务分组 --->')
                            if (res.errcode == 0) {
                                ElMessage.success('提交成功')
                                useTabs.close()
                            } else {
                                ElMessage.error(res.errmsg)
                            }
                        })
                        .finally(() => {})
                    this.loading = false
                } else {
                    console.log('error submit!!')
                    this.loading = false
                    return false
                }
            })
        },
        handleClick(event) {
            event.preventDefault()
            event.stopPropagation()
            // 或者直接不执行任何操作
        },
        // 处理json
        handleJson(obj) {
            if (obj.userLst) {
                obj.userLst = JSON.parse(obj.userLst)
            }
            for (let i in obj) {
                // console.log('22222222222222222', i)
                if (i == 'conditionNodes') {
                    obj[i].forEach((el) => {
                        el.userLst = JSON.parse(el.userLst)
                    })
                } else if (i == 'childNode') {
                    if (obj[i].type) {
                        obj[i].userLst = JSON.parse(obj[i].userLst)
                        if (obj[i].childNode.type) {
                            this.handleJson(obj)
                        }
                    }
                }
            }
        },
        // 处理jsonStr
        handleJsonStr(obj) {
            if (obj.userLst) {
                obj.userLst = JSON.stringify(obj.userLst)
            }
            for (let i in obj) {
                // console.log('22222222222222222', i)
                if (i == 'conditionNodes') {
                    obj[i].forEach((el) => {
                        el.userLst = JSON.stringify(el.userLst)
                    })
                } else if (i == 'childNode') {
                    if (obj[i].type) {
                        obj[i].userLst = JSON.stringify(obj[i].userLst)
                        if (obj[i].childNode.type) {
                            this.handleJsonStr(obj)
                        }
                    }
                }
            }
        },
        cancel() {
            useTabs.close()
        },
        // 获取任务分组
        getTaskGroup() {
            this.$HTTP
                .get('group/get_all')
                .then((res) => {
                    // console.log('获取任务分组 --->', res)
                    if (res.errcode == 0) {
                        // console.log('获取任务分组1 --->', res.result)
                        this.taskGroupList = res.result
                    } else {
                        ElMessage.error(res.errmsg)
                    }
                })
                .finally(() => {})
        },
        // 获取任务流
        getTaskFlow() {
            this.$HTTP
                .get('flow/get_all')
                .then((res) => {
                    // console.log('获取任务分组 --->', res)
                    if (res.errcode == 0) {
                        this.isrenwu=true
                        // console.log('获取任务分组1 --->', res.result)
                        this.folwList = res.result
                    } else {
                        ElMessage.error("没有任务流权限")
                        this.isrenwu=false
                    }
                })
                .finally(() => {})
        },
        // 改变任务流
        changeFlow(value) {
            console.log('任务流改变', value)
            this.folwList.forEach((el) => {
                if (el.id == value) {
                    let process = JSON.parse(el.process)
                    this.handleJsonStr(process.nodeConfig.childNode)
                    this.process = process
                }
            })
            this.recommendOptions = {
                api: 'task/get_referrer',
                post: {
                    id: value
                }
            }
        },
        handleRemove(file, fileList) {
            console.log(file, fileList)
            this.fileData = []
            fileList.forEach((el) => {
                let postData = {
                    name: el.name,
                    url: el.response.result,
                    size: (el.size / 1024).toFixed(2)
                }
                this.fileData.push(postData)
            })
        },
        handlePreview(file) {
            console.log(file)
            window.open(file.response.result, '_blank')
        },
        beforeRemove(file, fileList) {
            return this.$confirm(`确定移除 ${file.name}？`)
        },
        handleSuccess(res, file, fileList) {
            console.log('上传成功', res, file, fileList)
            this.fileData = []
            fileList.forEach((el) => {
                let postData = {
                    name: el.name,
                    url: el.response.result,
                    size: (el.size / 1024).toFixed(2)
                }
                this.fileData.push(postData)
            })
        }
    },
    mounted() {
        this.getTaskGroup()
        this.getTaskFlow()
    }
}
</script>

<style scoped>
.upload {
    width: 300px;
}
.flow {
    /* background-color: green; */
    pointer-events: none;
}
</style>