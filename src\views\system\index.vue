<template>
    <el-main>
        <el-card shadow="never">
            <el-form ref="form" :model="sys" label-width="100px" style="margin-top: 20px;">
                <el-form-item label="分配规则">
                    <el-select v-model="value" placeholder="请选择">
                        <el-option v-for="item in options" :key="item.id" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                    <div class="el-form-item-msg" data-v-b33b3cf8="">饱和模式：当人员有空闲时间时，会尽可能拆分任务并分配
                        。</div>
                    <div class="el-form-item-msg" data-v-b33b3cf8="">专注模式：整个任务会被尽可能地分配给可用人员，以确保任务的高效完成。</div>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submit">保存</el-button>
                </el-form-item>
            </el-form>
        </el-card>
    </el-main>
</template>

<script>
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
export default {
    name: 'system',
    data() {
        return {
            options: [{
                id: 1,
                value: '1',
                label: '饱和'
            }, { 
                id: 2,
                value: '0',
                label: '专注'
            },],
            value: ''
        }
    },
    created() {
        // 查看系统配置
        this.$HTTP
            .get('config/get_info')
            .then((res) => {
                if (res.errcode == 0) {
                    this.value = res.result.mode
                    this.value == 1 ? this.value = '饱和' : this.value = '专注'
                } else {
                    ElMessage.error(res.errmsg)
                }
            })
            .finally(() => {
            })
    },
    methods: {
        submit() {
            // 修改系统配置
            this.$HTTP
                .post('config/post_modify', {
                    mode: this.value
                })
                .then((res) => {
                    if (res.errcode == 0) {
                        ElMessage.success('提交成功!')
                    } else {
                        ElMessage.error(res.errmsg)
                    }
                })
                .finally(() => {

                })
        },
    }
}
</script>

<style>

</style>
