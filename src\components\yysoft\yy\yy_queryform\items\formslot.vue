<template>
    <!-- input -->
    <template v-if="item.component == 'input'">
        <el-input
            v-model="form[item.name]"
            :disabled="item.options.disabled"
            :type="item.options.type"
            :placeholder="item.options.placeholder"
            clearable
            :maxlength="item.options.maxlength"
            show-word-limit
        ></el-input>
    </template>
    <!-- inputtap -->
    <template v-else-if="item.component == 'inputtap'">
        <yy_inputtap
            y_inputtap
            :form="form"
            :colums="item"
        ></yy_inputtap>
    </template>
    <!-- fillvalue -->
    <template v-else-if="item.component == 'fillvalue'">
        <el-input
            v-model="item.options[form[item.name]]"
            :disabled="item.disabled"
            :type="item.options.type"
            :placeholder="item.options.placeholder"
            clearable
            :maxlength="item.options.maxlength"
            show-word-limit
        ></el-input>
    </template>
    <!-- array -->
    <template v-else-if="item.component == 'array'">
        <el-col
            v-for="array in item.optionArray"
            :key="array"
        >
            <span
                v-for="inde in JSON.parse(form[item.name])"
                :key="inde.id"
            >
                <el-input
                    v-model="inde[array.name]"
                    :disabled="item.options.disabled"
                    :type="item.options.type"
                    :placeholder="item.options.placeholder"
                    clearable
                    :maxlength="item.options.maxlength"
                    show-word-limit
                ></el-input>
            </span>
        </el-col>
    </template>
    <!-- arraycard -->
    <template v-else-if="item.component == 'arraycard'">
        <el-col
            v-for="(itm, ind) in form[item.name]"
            :key="ind"
            :span="8"
        >
            <el-card
                class="box-card"
                shadow="never"
            >
                <div
                    v-for="(im, id) in item.options.items"
                    :key="id"
                    class="box-card-item"
                >
                    <div class="label">{{ im.label }}</div>
                    <template v-if="im.component == 'select'">
                        <template
                            v-for="(imitems, iditems) in im.options.items"
                            :key="iditems"
                        >
                            <template v-if="imitems.value == itm[im.name]">
                                <el-tag
                                    class="value"
                                    :type="imitems.type"
                                >
                                    {{ imitems.label }}
                                </el-tag>
                            </template>
                        </template>
                    </template>
                    <template v-else>
                        <div class="value">{{ itm[im.name] }}</div>
                    </template>
                </div>
            </el-card>
        </el-col>
    </template>
    <!-- userlist -->
    <template v-else-if="item.component == 'userlist'">
        <div class="userlist">
            <span
                v-for="array in item.optionUser"
                :key="array"
            >
                <span
                    v-for="inde in JSON.parse(form[item.name])"
                    :key="inde.id"
                >
                    <span
                        v-for="obj in inde"
                        :key="obj.id"
                        style="color: #a8abb2"
                    >
                        {{ obj[array.name] + ' ' }}
                    </span>
                </span>
            </span>
        </div>
    </template>
    <!-- checkboxlimits  -->
    <template v-else-if="item.component == 'checkboxlimits'">
        <yy_checkbox
            v-model="form[item.name]"
            :model="item"
            :form="form"
            ref="yycheckbox"
        >
        </yy_checkbox>
    </template>
    <!-- tree -->
    <template v-else-if="item.component == 'treecheck'">
        <yy_tree
            v-model="form[item.name]"
            :model="item"
            :form="form"
        ></yy_tree>
    </template>
    <!-- checkbox -->
    <template v-else-if="item.component == 'checkbox'">
        <template v-if="item.name">
            <el-checkbox
                v-model="form[item.name][_item.name]"
                :label="_item.label"
                v-for="(_item, _index) in item.options.items"
                :key="_index"
            ></el-checkbox>
        </template>
        <template v-else>
            <el-checkbox
                v-model="form[_item.name]"
                :label="_item.label"
                v-for="(_item, _index) in item.options.items"
                :key="_index"
            ></el-checkbox>
        </template>
    </template>
    <!-- checkboxGroup -->
    <template v-else-if="item.component == 'checkboxGroup'">
        <el-checkbox-group v-model="form[item.name]">
            <el-checkbox
                v-for="_item in item.options.items"
                :key="_item.value"
                :label="_item.value"
                >{{ _item.label }}</el-checkbox
            >
        </el-checkbox-group>
    </template>
    <!-- upload -->
    <template v-else-if="item.component == 'upload'">
        <el-col
            v-for="(_item, _index) in item.options.items"
            :key="_index"
        >
            <el-form-item :prop="_item.name">
                <sc-upload
                    v-model="form[_item.name]"
                    :title="_item.label"
                ></sc-upload>
            </el-form-item>
        </el-col>
    </template>
    <!-- switch -->
    <template v-else-if="item.component == 'switch'">
        <template v-if="item.options">
            <el-switch
                v-model="form[item.name]"
                :active-value="item.options.activevalue"
                :inactive-value="item.options.inactivevalue"
            />
        </template>
        <template v-else>
            <el-switch v-model="form[item.name]" />
        </template>
    </template>
    <!-- select -->
    <template v-else-if="item.component == 'select'">
        <yy_select
            ref="yyselect"
            v-model="form[item.name]"
            :model="item.name"
            :form="form"
            :remote="item.options.remote"
            :items="item.options.items"
            :remoteitems="item.options.remoteitems"
            :multiple="item.options.multiple"
            :getselectremote="getselectremote"
            :disabled="item.options.disabled"
            :name="item.name"
            :relatedata="item.options.relatedata"
            :placeholder="item.options.placeholder"
            @selectlist="selectlist"
            @getremotedata="getremotedata"
        >
        </yy_select>
    </template>
    <!-- selectUser -->
    <template v-else-if="item.component == 'selectUser'">
        <yy_selectuser
            v-model="form[item.name]"
            :model="form[item.name]"
            :items="item.options"
            :query="true"
        >
        </yy_selectuser>
    </template>
    <!-- cascader -->
    <template v-else-if="item.component == 'cascader'">
        <yy_cascader
            v-model="form[item.name]"
            :placeholder="item.options.placeholder"
            :props="item.options.props"
            :remote="item.options.remote"
            :form="form"
            clearable
        ></yy_cascader>
    </template>
    <!-- selectitems -->
    <template v-else-if="item.component == 'selectitems'">
        <yy_selectitems
            v-model="form[item.name]"
            :placeholder="item.options.placeholder"
            :props="item.options.props"
            :remote="item.options.remote"
            :item="item"
            :form="form"
            clearable
        ></yy_selectitems>
    </template>
    <!-- date -->
    <template v-else-if="item.component == 'date'">
        <el-date-picker
            v-model="form[item.name]"
            :type="item.options.type"
            :range-separator="item.options.rangeseparator"
            :start-placeholder="item.options.startplaceholder"
            :end-placeholder="item.options.endplaceholder"
            :shortcuts="item.options.shortcuts"
            :default-time="item.options.defaultTime"
            :value-format="item.options.valueFormat"
            :placeholder="item.options.placeholder || '请选择'"
            :align="item.options.align"
            :format="item.options.format"
            :disabledDate="item.options.disabledDate"
            :picker-options="item.options.pickeroptions"
            @change="showDate"
        ></el-date-picker>
    </template>
    <!-- time -->
    <template v-else-if="item.component == 'time'">
        <el-time-select
            v-model="form[item.name]"
            arrow-control
            :picker-options="item.options.pickeroptions"
            :placeholder="item.options.placeholder || '选择时间'"
        >
        </el-time-select>
    </template>
    <!-- timepicker -->
    <template v-else-if="item.component == 'timepicker'">
        <el-time-picker
            v-model="form[item.name]"
            :value-format="item.options.valueFormat"
            :picker-options="item.options.pickeroptions"
            :placeholder="item.options.placeholder || '选择时间'"
        >
        </el-time-picker>
    </template>
    <!-- number -->
    <template v-else-if="item.component == 'number'">
        <el-input-number
            v-model="form[item.name]"
            :min="item.options.min"
            :max="item.options.max"
            controls-position="right"
        ></el-input-number>
    </template>
    <!-- radio -->
    <template v-else-if="item.component == 'radio'">
        <el-radio-group v-model="form[item.name]">
            <el-radio
                v-for="_item in item.options.items"
                :key="_item.value"
                :label="_item.value"
                >{{ _item.label }}</el-radio
            >
        </el-radio-group>
    </template>
    <!-- color -->
    <template v-else-if="item.component == 'color'">
        <el-color-picker v-model="form[item.name]" />
    </template>
    <!-- rate -->
    <template v-else-if="item.component == 'rate'">
        <el-rate
            style="margin-top: 6px"
            v-model="form[item.name]"
        ></el-rate>
    </template>
    <!-- slider -->
    <template v-else-if="item.component == 'slider'">
        <el-slider
            v-model="form[item.name]"
            :marks="item.options.marks"
        ></el-slider>
    </template>
    <!-- tableselect -->
    <template v-else-if="item.component == 'tableselect'">
        <tableselect-render
            v-model="form[item.name]"
            :item="item"
        ></tableselect-render>
    </template>
    <!-- editor -->
    <template v-else-if="item.component == 'editor'">
        <sc-editor
            v-model="form[item.name]"
            placeholder="请输入"
            :height="400"
        ></sc-editor>
    </template>
    <!-- tag -->
    <template v-else-if="item.component == 'tag'">
        <template
            v-for="(_item, _index) in item.options.items"
            :key="_index"
        >
            <el-tag
                v-if="form[item.name] == _item.value"
                :type="_item.type"
                >{{ _item.label }}</el-tag
            >
        </template>
    </template>
    <!-- noComponent -->
    <template v-else>
        <el-tag type="danger">[{{ item.component }}] Component not found</el-tag>
    </template>
</template>

<script>
import { dayjs } from 'element-plus'
export default {
    props: {
        item: {
            type: Object,
            default: () => {}
        },
        form: { type: Object, default: () => {} }
        // getselectremote: { type: Boolean, default: false }
    },
    emits: ['weekdata'],
    data() {
        return {
            // isSave: false
            start: '',
            end: '',
            temform: {}
        }
    },
    watch: {
        form(val) {
            if (this.item.component == 'date') {
                if (this.item.options.type == 'week') {
                    this.showDate(this.item.options.value)
                }
            }
        }
    },
    mounted() {
        // console.log('---------')
        // console.log(this.item)
        // console.log(JSON.stringify(this.form))
        // this.temform[this.item.name] = this.item.value
        // this.form = this.temform
        var time = this.$TOOL.dateFormat(new Date())
        this.showDate(time)
    },
    methods: {
        //处理下拉数据格式
        selectlist(postlock, name, formtype) {
            if (typeof postlock === 'object' && !Array.isArray(postlock)) {
                if (formtype) {
                    for (const key in postlock) {
                        this.form[key] = postlock[key]
                    }
                }
            } else if (Array.isArray(postlock)) {
                if (formtype) {
                    this.form[name] = JSON.stringify(postlock)
                }
            }
        },

        showDate(value) {
            if (this.item.options && this.item.options.type == 'week') {
                let startTime = dayjs(value).startOf('week').format('YYYY-MM-DD')
                let endTime = dayjs(value).endOf('week').format('YYYY-MM-DD')
                var postdata = {}
                postdata[this.item.options.starttime] = startTime
                postdata[this.item.options.endtime] = endTime
                this.item.message = `${startTime}至${endTime}`
                this.$emit('weekdata', postdata)
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.box-card {
    width: 400px;
    .box-card-item {
        display: flex;
        .label {
            width: 120px;
        }
    }
}
</style>
