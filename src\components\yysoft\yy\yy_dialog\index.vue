<template>
    <!-- button -->
    <yy_button
        :type="options.type"
        :label="options.label"
        :component="options.component"
        :options="options.options"
        :value="form"
        plain
        :size="size"
        @finish-event="button_finish_event"
        @dialog="dialog"
    ></yy_button>
    <!-- dialog -->
    <el-dialog
        v-model="dialogVisible"
        center
        width="30%"
        :before-close="handleClose"
        append-to-body
    >
        <!-- form -->
        <template v-if="options.options.component == 'form'">
            <yy_form
                ref="form"
                :status="options.options.remote.state"
                :modelValue="modelValue"
                :filed="dialogformitems"
                @submit="submit"
                @goBack="handleClose"
            ></yy_form>
        </template>
    </el-dialog>
</template>

<script>
import contentslot from './items/contentslot.vue'
import { ElMessage, ElNotification } from 'element-plus'

export default {
    name: 'yy_dialog',
    components: {
        contentslot
    },
    props: {
        // 配置内容
        options: { type: Object, default: () => {} },
        // 表格信息
        form: { type: Object, default: () => {} },
        size: { type: String, default: '' }
    },
    data() {
        return {
            // 遮罩层
            dialogVisible: false,
            // 传递数据
            modelValue: {},
            // 文件配置
            dialogformitems: [],
            // 遮罩层名
            tips: '',
            // 遮罩层远程信息
            dialogremote: {}
        }
    },
    mounted() {
        // console.log(this.options)
        if (this.options.options.component == 'form') {
            let cloumns = this.options.options.columns
            cloumns.forEach((el) => {
                if (el.component == 'select') {
                    if (el.options.remote) {
                        if (!el.options.remote.notpreload) {
                            this.$HTTP.get(el.options.remote.api).then((res) => {
                                if (res.errcode != 0) {
                                    ElMessage.error(res.errmsg)
                                } else {
                                    el.options.remoteitems = res.result
                                } 
                            })
                        }
                    }
                }
            })
        }
    },
    methods: {
        opendialog() {
            this.dialogVisible = true
        },
        handleClose() {
            this.dialogVisible = false
        },
        // 遮罩层事件
        async dialog(i) {
            // console.log('-----------------')
            // console.log('iiiiiii', i)
            this.dialogVisible = true
            this.dialogremote = i
            this.dialogformitems = i.columns
            if (this.dialogremote.remote.api) {
                // 获取数据信息
                await this.$HTTP.get(i.remote.api, i.remote.data).then((res) => {
                    if (res.errcode != 0) {
                        ElMessage.error(res.errmsg)
                    } else {
                        // this.form = res.result
                        this.modelValue = res.result
                        // console.log('----123221-------------')
                        // console.log('modelvalue', res.result)
                    }
                })
            }
        },
        handleClose() {
            this.dialogVisible = false
            // this.$emit('refresh', this.dialogVisible)
        },
        submit(form) {
            // console.log('----------------------------------------')
            // console.log('forrrrrm', this.dialogremote.remote)
            // console.log('forrrrrm', form)
            let remote = this.dialogremote.remote
            if (remote.submitData) {
                var postdata = {}
                remote.submitData.forEach((el) => {
                    postdata[el] = form[el]
                })
                form = Object.assign(postdata, remote.data)
            } else {
                form = Object.assign(form, remote.data)
            }
            this.$HTTP
                .post(this.dialogremote.remote.edit, form)
                .then((res) => {
                    if (res.errcode != 0) {
                        ElMessage.error(res.errmsg)
                    } else {
                        // 数据获取成功
                        ElNotification({
                            title: '操作成功',
                            type: 'success'
                        })
                        this.dialogVisible = false
                        this.$refs.form.changebuttonloading()
                        this.$emit('refresh', this.dialogVisible)
                    }
                })
                .finally(() => {})
        }
    }
}
</script>

<style>
</style>