<template>
    <div class="box">
        <el-button
            style="width: 100px"
            @click="openDialog"
        >
            {{ label }}
        </el-button>
        <div class="tag">
            <!-- {{ this.formDataTem }} -->
            <template
                v-for="(item, index) in this.formDataTem"
                :key="index"
            >
                <div class="tag-item">
                    <el-tag>{{ item[this.name] }}</el-tag>
                </div>
            </template>
        </div>
    </div>

    <el-dialog
        calss="el-dialog"
        v-model="dialogVisible"
        :title="label"
        width="40%"
        :before-close="handleClose"
    >
        <el-checkbox
            :indeterminate="isIndeterminate"
            v-model="checkAll"
            @change="handleCheckAllChange"
            >全选</el-checkbox
        >
        <div class="checkbox">
            <div
                v-for="(item, index) in this.selectdata"
                :key="index"
            >
                <el-checkbox-group
                    v-model="postData"
                    @change="handleCheckedChangeOne"
                >
                    <!-- 子菜单-->
                    <el-checkbox
                        :label="item"
                        :indeterminate="item.isIndeterminate"
                        >{{ item[this.name] }}
                    </el-checkbox>
                </el-checkbox-group>
                <!-- <div style="padding: 5px 15px">
                    <el-checkbox-group
                        v-model="item.postData"
                        @change="handleCheckedChangeTwo(val, item)"
                    >
                        <el-checkbox
                            v-for="(it, inx) in item[this.childrenProp]"
                            :key="inx"
                            :label="it"
                            >{{ it[this.name] }}
                        </el-checkbox>
                    </el-checkbox-group>
                </div> -->
                <div class="divider"></div>
            </div>
        </div>
        <div class="btn">
            <el-button
                class="query_btn"
                type="primary"
                @click="goSubmit"
                >提交</el-button
            >
        </div>
    </el-dialog>
</template>

<script>
import { ElMessageBox } from 'element-plus'
export default {
    name: 'yy_treeDialog',
    data() {
        return {
            checkAll: false,
            isIndeterminate: false,
            dialogVisible: false,
            selectdata: [],
            checkedCities: [],
            postData: [],
            formDataTem: [],
            temCheckOne: []
        }
    },
    props: {
        model: { type: String, default: '' },
        label: { type: String, default: '' },
        childrenProp: { type: String, default: '' },
        names: { type: String, default: '' },
        name: { type: String, default: '' },
        remote: { type: Object, default: () => {} },
        defaultProps: { type: Object, default: () => {} },
        form: { type: Object, default: () => {} }
    },
    watch: {
        postData: {
            handler(val) {
                // console.log('-----postData---')
                // console.log(val)
                // console.log('检查 length 报错', val)
                if (val.length == 0 || val.length == this.selectdata.length) {
                    this.isIndeterminate = false
                    if (val.length == this.selectdata.length) {
                        this.checkAll = true
                    }
                } else {
                    this.isIndeterminate = true
                }
            },
            deep: true
        }
        // form: {
        //     handler(val) {
        //         console.log('-----------------')
        //         console.log(this.names)
        //         var isEmpty = false
        //         for (var i in val) {
        //             console.log('检查 length 报错', i, val[i])
        //             if (i == this.names) {
        //                 if (val[i].length == 0) {
        //                     isEmpty = true
        //                 }
        //             }
        //         }
        //         // console.log('到这里了么')
        //         if (isEmpty) {
        //             this.postData = []
        //             // this.formDataTem = []
        //             this.selectdata.forEach((el) => {
        //                 el.postData = []
        //                 el.isIndeterminate = false
        //             })
        //         }
        //     },
        //     deep: true
        // }
    },
    mounted() {
        // console.log('----46----')
        // console.log(this.form)
        // this.getData() 
        // console.log(this.selectdata)
    },
    methods: {
        getData() {
            // console.log(this.remote)
            // console.log('getdata')
            this.$HTTP.get(this.remote.api).then((res) => {
                if (res.errcode != 0) {
                    ElMessage.error(res.errmsg)
                } else {
                    // console.log(this.data)
                    var data = res.result
                    data.forEach((el) => {
                        // el.selected = false
                        el.isIndeterminate = false
                        el.postData = []
                        if (typeof el[this.childrenProp] == 'string') {
                            el[this.childrenProp] = JSON.parse(el[this.childrenProp])
                            // el[this.childrenProp].forEach((item) => {
                            //     item.selected = false
                            // })
                        }
                    })
                    this.selectdata = data
                    // 处理数据
                    if (this.form[this.names].length >= 1) {
                        this.formDataTem = JSON.parse(this.form[this.names])
                        // console.log(this.formDataTem)
                        this.selectdata.forEach((value) => {
                            this.formDataTem.forEach((el) => {
                                // console.log(el)
                                if (value.id == el.id) {
                                    // console.log(value)
                                    // console.log(el)
                                    if (el[this.childrenProp].length == value[this.childrenProp].length) {
                                        value.postData = value[this.childrenProp]
                                        this.postData.push(value)
                                    } else {
                                        value.isIndeterminate = true
                                        // console.log(el[this.childrenProp])
                                        // console.log(value[this.childrenProp])
                                        // value.postData = JSON.parse(JSON.stringify(el[this.childrenProp]))
                                        value[this.childrenProp].forEach((j) => {
                                            el[this.childrenProp].forEach((k) => {
                                                if (JSON.stringify(j) == JSON.stringify(k)) {
                                                    value.postData.push(j)
                                                }
                                            })
                                        })
                                    }
                                }
                            })
                        })
                    }
                }
            })
        },
        // 全选
        handleCheckAllChange(val) {
            if (this.checkAll) {
                this.postData = this.selectdata
                this.selectdata.forEach((el) => {
                    el.postData = el[this.childrenProp]
                    el.isIndeterminate = false
                })
            } else {
                this.postData = []
                this.selectdata.forEach((el) => {
                    el.postData = []
                    el.isIndeterminate = false
                })
            }
        },
        // 一级菜单变化
        handleCheckedChangeOne(val) {
            // console.log('一级菜单变化值 --->', val, this.selectdata)
            // console.log('判断增加还是减少 --->', val.length, this.temCheckOne.length)
            // let changeData = []
            if (val.length > this.temCheckOne.length) {
                // 增加
                console.log('增加流程', val, this.temCheckOne)
                val.forEach((el) => {
                    let isHave = false
                    this.temCheckOne.forEach((item) => {
                        if (el.title == item.title) {
                            isHave = true
                        }
                    })
                    if (!isHave) {
                        el.isIndeterminate = false
                        el.postData = el[this.childrenProp]
                    }
                })
            } else {
                // 减少
                console.log('减少流程', val, this.temCheckOne)
                let handelEl = {}
                this.temCheckOne.forEach((item) => {
                    let isDel = true
                    val.forEach((el) => {
                        if (el.title == item.title) {
                            isDel = false
                        }
                    })
                    if (isDel) {
                        handelEl = item
                    }
                })

                this.selectdata.forEach((el) => {
                    if (el.title == handelEl.title) {
                        el.isIndeterminate = false
                        el.postData = []
                    }
                })
            }
            this.temCheckOne = JSON.parse(JSON.stringify(val))
            // this.selectdata.forEach((el) => {
            // el.postData = []
            // })
            // val.forEach((el) => {
            //     el.postData = el[this.childrenProp]
            // })
        },
        // 二级菜单变化
        handleCheckedChangeTwo(val, item) {
            if (item.postData.length == item[this.childrenProp].length) {
                // 全选
                item.isIndeterminate = false
                this.postData.push(item)
            } else if (item.postData.length == 0) {
                // 全部选
                item.isIndeterminate = false
                this.postData.forEach((value, index, array) => {
                    if (value.id == item.id) {
                        array.splice(index, 1)
                    }
                })
            } else {
                // 其他情况
                // console.log('二级菜单其他配置', item)
                item.isIndeterminate = true
            }
        },
        // 确定
        goSubmit() {
            // console.log('确定-----')
            // console.log(this.data)
            this.dialogVisible = false
            var formData = JSON.parse(JSON.stringify(this.selectdata))
            this.formDataTem = []
            formData.forEach((value) => {
                // console.log('测试看 length 问题', value.postData)
                if (value.postData.length > 0) {
                    this.formDataTem.push(value)
                }
            })
            // console.log('处理结果数据', this.formDataTem)
            this.handlerData(this.formDataTem)
        },
        // 处理发送数据
        handlerData(val) {
            var temdata = JSON.parse(JSON.stringify(val))
            temdata.forEach((el) => {
                el[this.childrenProp] = el.postData
            })
            // console.log(this.form)
            this.form[this.names] = JSON.stringify(temdata)
        },
        // 返回
        goBack() {
            this.dialogVisible = false
            // this.checkAll = false
            // this.postData = []
            // this.data.forEach((el) => {
            //     el.postData = []
            //     el.isIndeterminate = false
            // })
        },
        // 打开遮罩层
        openDialog() {
            this.dialogVisible = true
            this.isIndeterminate = false
            // this.temCheckOne = JSON.parse(JSON.stringify(this.postData))
            this.getData()
        }
    }
}
</script>

<style lang="scss" scoped>
.box {
    display: flex;
    flex-direction: column;

    .tag {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
        .tag-item {
            margin-right: 20px;
            //     width: 100px;
        }
    }
}

.el-dialog {
    height: 80%;

    .checkbox {
        height: 600px;
        overflow: auto;

        .divider {
            width: 80%;
            height: 1px;
            background-color: rgb(218, 218, 218);
        }
    }

    .btn {
        margin-top: 10px;
    }
}
</style>