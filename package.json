{"name": "scui", "version": "1.6.6", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "2.0.6", "@tinymce/tinymce-vue": "5.0.0", "axios": "0.27.2", "codemirror": "5.65.5", "core-js": "3.24.1", "cropperjs": "1.5.12", "crypto-js": "4.1.1", "dingtalk-jsapi": "^3.0.12", "echarts": "5.3.3", "element-plus": "2.2.12", "file-saver": "^2.0.5", "jspdf": "^2.5.1", "jspdf-autotable": "^3.5.29", "nprogress": "0.2.0", "pinyin-match": "^1.2.2", "sortablejs": "1.15.0", "tinymce": "6.1.2", "vue": "3.2.37", "vue-i18n": "9.2.2", "vue-router": "4.1.3", "vuedraggable": "4.0.3", "vuex": "4.0.2", "xgplayer": "2.31.7", "xgplayer-hls": "2.5.2", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "^3.0.3", "sass": "1.37.5", "vite": "^3.0.7", "vite-plugin-compression": "^0.5.1"}, "eslintConfig": {"root": true, "env": {"node": true}, "globals": {"APP_CONFIG": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {"indent": 0, "no-tabs": 0, "no-mixed-spaces-and-tabs": 0, "vue/no-unused-components": 0, "vue/multi-word-component-names": 0}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}