<!--
 * @Descripttion: 文件导出
 * @version: 1.1
 * @Author: sakuya
 * @Date: 2022年5月24日16:20:12
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-12-15 15:46:26
-->

<template>
    <slot :open="open">
        <el-button
            type="primary"
            icon="sc-icon-download"
            @click="open"
            >导出</el-button
        >
    </slot>
    <el-drawer
        v-model="dialog"
        title="导出"
        :size="400"
        direction="rtl"
        append-to-body
        destroy-on-close
    >
        <el-main style="padding: 0 20px 20px 20px">
            <div
                v-loading="downLoading"
                element-loading-text="正在处理中..."
            >
                <div
                    v-if="downLoading && progress"
                    style="
                        position: absolute;
                        width: 100%;
                        height: 100%;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        z-index: 3000;
                    "
                >
                    <el-progress
                        :text-inside="true"
                        :stroke-width="20"
                        :percentage="downLoadProgress"
                        style="width: 100%; margin-bottom: 120px"
                    />
                </div>
                <el-tabs>
                    <el-tab-pane
                        label="常规"
                        lazy
                    >
                        <el-form
                            label-width="100px"
                            label-position="left"
                            style="margin: 10px 0 20px 0"
                        >
                            <el-form-item label="文件名">
                                <el-input
                                    v-model="formData.fileName"
                                    placeholder="请输入文件名"
                                />
                            </el-form-item>
                            <el-form-item label="文件类型">
                                <el-select
                                    v-model="formData.fileType"
                                    placeholder="请选择文件类型"
                                >
                                    <el-option
                                        v-for="item in fileTypes"
                                        :key="item"
                                        :label="'*.' + item"
                                        :value="item"
                                    />
                                </el-select>
                            </el-form-item>
                            <!-- <slot
                                name="form"
                                :formData="formData"
                            >
                                <el-form-item label="导出条数">
                                    <el-select
                                        v-model="formData.limit"
                                        placeholder="Select"
                                    >
                                        <el-option
                                            label="100条"
                                            value="100"
                                        />
                                        <el-option
                                            label="500条"
                                            value="500"
                                        />
                                        <el-option
                                            label="1000条"
                                            value="1000"
                                        />
                                        <el-option
                                            label="5000条"
                                            value="5000"
                                        />
                                        <el-option
                                            label="10000条"
                                            value="10000"
                                        />
                                    </el-select>
                                </el-form-item>
                            </slot> -->
                        </el-form>
                        <!-- <el-button
                            v-if="async"
                            type="primary"
                            size="large"
                            icon="el-icon-plus"
                            style="width: 100%"
                            @click="download"
                            :loading="asyncLoading"
                            >发起导出任务</el-button
                        > -->
                        <el-button
                            type="primary"
                            size="large"
                            icon="el-icon-download"
                            style="width: 100%"
                            @click="download"
                            >下 载</el-button
                        >
                    </el-tab-pane>
                    <el-tab-pane
                        label="列设置"
                        v-if="columnData.length > 0"
                        lazy
                    >
                        <columnSet
                            :column="columnData"
                            v-model="usercolumn"
                        ></columnSet>
                    </el-tab-pane>
                    <el-tab-pane
                        label="其他参数"
                        v-if="data && showData"
                        lazy
                    >
                        <el-descriptions
                            :column="1"
                            border
                            size="small"
                        >
                            <el-descriptions-item
                                v-for="(val, key) in data"
                                :key="key"
                                :label="key"
                                >{{ val }}</el-descriptions-item
                            >
                        </el-descriptions>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </el-main>
    </el-drawer>
</template>

<script>
import columnSet from './column'
import PdfLoader from '@/utils/html2pdf'
import './simheiNormal'
import jsPDF from 'jspdf'
import autoTable from 'jspdf-autotable'

export default {
    name: 'yy_export',
    components: {
        columnSet
    },
    props: {
        url: { type: String, default: () => ' ' },
        type: { type: String, default: 'blob' },
        switch: { type: Object, default: () => {} },
        query: { type: Object, default: () => {} },
        column: { type: Array, default: () => [] },
        dynamicColumns: { type: Array, default: () => [] },
        fileName: { type: String, default: '' },
        showsummary: { type: Boolean, default: false },
        fileTypes: { type: Array, default: () => ['xlsx'] },
        data: { type: Object, default: () => {} },
        showData: { type: Boolean, default: false },
        progress: { type: Boolean, default: true },
        handleStr: { type: Array, default: () => [] }
    },
    data() {
        return {
            usercolumn: {},

            dialog: false,
            formData: {
                fileName: this.fileName,
                fileType: this.fileTypes[0]
            },
            downLoading: false,
            downLoadProgress: 0,
            asyncLoading: false,
            pdfHeader: [],
            pdfData: [],
            multitable: false,
            header: []
        }
    },
    watch: {
        // 'formData.fileType'(val) {
        //     console.log('-----------------')
        //     console.log(this.formData.fileName)
        //     if (this.formData.fileName.includes('.')) {
        //         this.formData.fileName =
        //             this.formData.fileName.substring(0, this.formData.fileName.lastIndexOf('.')) + '.' + val
        //     } else {
        //         this.formData.fileName = this.formData.fileName + '.' + val
        //     }
        // }
    },
    created() {
        this.fileName
    },
    mounted() {
        const exportPdf = document.getElementById('exportPdf') // 需要导出部分页面的id名
        this.pdfDownLoader = new PdfLoader(exportPdf) // fileName -->导出文件名,  question-table -->防止被截断的class名
    },
    methods: {
        open() {
            this.dialog = true
            this.formData = {
                // fileName: (this.fileName ? this.fileName : new Date().getTime() + '') + '.' + this.fileTypes[0],
                fileName: this.fileName,
                fileType: this.fileTypes[0]
            }
            this.columnData = JSON.parse(JSON.stringify(this.column))
        },
        close() {
            this.dialog = false
        },
        download() {
            let columnArr = {
                column: this.columnData
                    .filter((n) => !n.hide)
                    .map((n) => n.prop)
                    .join(',')
            }
            let assignData = { ...this.query, ...this.formData, ...columnArr, download: 1, ...this.data }
            switch (this.type) {
                case 'blob':
                    this.downloadFile(this.url, this.formData.fileName, assignData)
                    break
                case 'async':
                    this.asyncDownload(this.url, this.formData.fileName, assignData)
                    break
                case 'url':
                    this.linkFile(this.url, this.formData.fileName, assignData)
                    break
            }
        },
        linkFile(url, fileName, data = {}) {
            let a = document.createElement('a')
            a.style = 'display: none'
            a.target = '_blank'
            a.download = fileName
            a.href = url + this.toQueryString(data)
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
        },
        downloadFile(apiObj, fileName, data = {}) {
            // console.log('-----------------------------------')
            // console.log(this.formData.fileType)
            this.downLoading = true
            this.$HTTP
                .post(apiObj, data)
                .then((res) => {
                    // console.log(77777777, res)
                    this.downLoading = false
                    this.downLoadProgress = 0
                    if (this.dynamicColumns.length) {
                        this.handleDynamicColumns(this.dynamicColumns, res.result.data || res.result)
                    }
                    // console.log(88888888, res)
                    import('@/utils/Export2Excel').then((excel) => {
                        var header = []
                        var multiHeader = []
                        var filterVal = []
                        var multitable = false
                        var merges = []
                        let excel_row = [
                            'A',
                            'B',
                            'C',
                            'D',
                            'E',
                            'F',
                            'G',
                            'H',
                            'I',
                            'J',
                            'K',
                            'L',
                            'M',
                            'N',
                            'O',
                            'P',
                            'Q',
                            'R',
                            'S',
                            'T',
                            'U',
                            'V',
                            'W',
                            'X',
                            'Y',
                            'Z'
                        ]
                        this.columnData.forEach((rs) => {
                            if (rs.hide != true) {
                                if (rs.children) {
                                    multitable = true
                                    multiHeader.push(rs.label)
                                    rs.children.forEach((sub, sub_index) => {
                                        header.push(sub.label)
                                        filterVal.push(sub.prop)
                                        if (sub_index > 0) multiHeader.push('')
                                        if (sub.options) {
                                            res.result.data.forEach((el) => {
                                                el[sub.prop] = sub.options[el[sub.prop]]
                                            })
                                        }
                                    })
                                } else {
                                    if (rs.label != '操作') {
                                        multiHeader.push(rs.label)
                                        header.push('')
                                        filterVal.push(rs.prop)
                                        if (rs.options) {
                                            if (typeof rs.options[0] === 'string') {
                                                if (Array.isArray(res.result)) {
                                                    res.result.forEach((el) => {
                                                        el[rs.prop] = rs.options[el[rs.prop]]
                                                    })
                                                } else if (res.result.data && Array.isArray(res.result.data)) {
                                                    res.result.data.forEach((el) => {
                                                        el[rs.prop] = rs.options[el[rs.prop]]
                                                    })
                                                }
                                            } else {
                                                if (Array.isArray(res.result)) {
                                                    res.result.forEach((el) => {
                                                        if (el[rs.prop] || el[rs.prop] == '0') {
                                                            if (Array.isArray(el[rs.prop])) {
                                                                el[rs.prop] = el[rs.prop].map((dish) => {
                                                                    let text = ''
                                                                    rs.options.forEach((cof) => {
                                                                        text += `${dish[cof.prop]} `
                                                                    })
                                                                    return text
                                                                })
                                                                el[rs.prop] = el[rs.prop].join(',')
                                                            }
                                                            if (this.switch) {
                                                                if (rs.prop == this.switch.name) {
                                                                    el[rs.prop] = this.switch.items[el[rs.prop]].label
                                                                }
                                                            }
                                                        }
                                                    })
                                                } else if (res.result.data && Array.isArray(res.result.data)) {
                                                    res.result.data.forEach((el) => {
                                                        if (el[rs.prop] || el[rs.prop] == '0') {
                                                            if (Array.isArray(el[rs.prop])) {
                                                                el[rs.prop] = el[rs.prop].map((dish) => {
                                                                    let text = ''
                                                                    rs.options.forEach((cof) => {
                                                                        text += `${dish[cof.prop]} `
                                                                    })
                                                                    return text
                                                                })
                                                                el[rs.prop] = el[rs.prop].join(', ')
                                                            }
                                                            console.log('报错')
                                                            if (this.switch) {
                                                                if (rs.prop == this.switch.name) {
                                                                    el[rs.prop] = this.switch.items[el[rs.prop]].label
                                                                }
                                                            }
                                                        }
                                                    })
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        })

                        const data = this.formatJson(filterVal, res.result.data || res.result)

                        console.log('-------------------')
                        console.log(data)

                        let m = Math.floor(header.length / 26)
                        if (m) {
                            let ls = []
                            for (let i = 0; i < m; i++) {
                                excel_row.forEach((E) => {
                                    ls.push(`${excel_row[i]}${E}`)
                                })
                            }
                            excel_row = excel_row.concat(ls)
                        }
                        if (multitable) {
                            //解决合并项
                            var tip = ''
                            var end_tip = ''
                            multiHeader.forEach((rs, index) => {
                                if (!rs) {
                                    end_tip = excel_row[index] + '1'
                                } else {
                                    if (end_tip) {
                                        merges.push(tip + end_tip)
                                        end_tip = ''
                                    }
                                    tip = excel_row[index] + '1:'
                                }
                            })
                            if (end_tip) {
                                merges.push(tip + end_tip)
                                end_tip = ''
                            }
                            //解决二层合并项
                            header.forEach((rs, index) => {
                                if (header[index] == '') {
                                    merges.push(excel_row[index] + '1:' + excel_row[index] + '2')
                                }
                            })

                            var arr = []
                            arr.push(multiHeader)
                            if (this.formData.fileType == 'xlsx') {
                                excel.export_json_to_excel({
                                    multiHeader: arr,
                                    header,
                                    data,
                                    filename: this.formData.fileName,
                                    merges,
                                    autoWidth: false
                                })
                            } else if (this.formData.fileType == 'pdf') {
                                this.multitable = true
                                const exportPdf = document.getElementById('exportPdf') // 需要导出部分页面的id名
                                var multiHeaderObj = []
                                var headerObj = []
                                // var temMultiHeaderIndex = 0
                                for (let i in header) {
                                    // 如果此项不存在
                                    if (!header[i]) {
                                        var itemObj = {
                                            content: multiHeader[i],
                                            rowSpan: 2,
                                            styles: { halign: 'center' }
                                        }
                                        multiHeaderObj.push(itemObj)
                                    } else {
                                        if (multiHeader[i]) {
                                            var itemObj = {
                                                content: multiHeader[i],
                                                colSpan: 1
                                            }
                                            multiHeaderObj.push(itemObj)
                                            // temMultiHeaderIndex = i
                                        } else {
                                            var col = multiHeaderObj[multiHeaderObj.length - 1].colSpan
                                            multiHeaderObj[multiHeaderObj.length - 1].colSpan = col + 1
                                        }
                                        headerObj.push(header[i])
                                    }
                                }
                                this.pdfHeader = multiHeaderObj
                                this.header = headerObj
                                this.pdfData = data
                                const doc = new jsPDF('l', 'pt', 'a4', false)
                                doc.setFont('simhei')
                                doc.autoTable({
                                    head: [this.pdfHeader, this.header],
                                    body: this.pdfData,
                                    styles: {
                                        fillColor: [255, 255, 255],
                                        font: 'simhei',
                                        fontStyle: 'normal',
                                        textColor: [0, 0, 0],
                                        halign: 'center'
                                    }, // 表格样式
                                    theme: 'grid',
                                    headStyles: {
                                        fillColor: [46, 128, 186],
                                        font: 'simhei',
                                        fontStyle: 'normal',
                                        textColor: [255, 255, 255],
                                        halign: 'center'
                                    }
                                })
                                doc.save(this.formData.fileName)
                            }
                        } else {
                            if (this.formData.fileType == 'xlsx') {
                                excel.export_json_to_excel({
                                    header: multiHeader,
                                    data,
                                    filename: this.formData.fileName,
                                    autoWidth: false
                                })
                            } else if (this.formData.fileType == 'pdf') {
                                this.pdfHeader = multiHeader
                                this.pdfData = data
                                const doc = new jsPDF('l', 'pt', 'a4', false)
                                doc.setFont('simhei')
                                doc.autoTable({
                                    head: [this.pdfHeader],
                                    body: this.pdfData,
                                    styles: {
                                        fillColor: [255, 255, 255],
                                        font: 'simhei',
                                        fontStyle: 'normal',
                                        textColor: [0, 0, 0],
                                        halign: 'center'
                                    }, // 表格样式
                                    theme: 'grid',
                                    headStyles: {
                                        fillColor: [46, 128, 186],
                                        font: 'simhei',
                                        fontStyle: 'normal',
                                        textColor: [255, 255, 255],
                                        halign: 'center'
                                    }
                                })
                                doc.save(this.formData.fileName)
                            }
                        }
                        this.dialog = false
                    })
                })
                .catch((err) => {
                    this.downLoading = false
                    this.downLoadProgress = 0
                    this.$notify.error({
                        title: '下载文件失败',
                        message: err
                    })
                })
        },
        asyncDownload(apiObj, fileName, data = {}) {
            this.asyncLoading = true
            apiObj
                .get(data)
                .then((res) => {
                    this.asyncLoading = false
                    if (res.code == 200) {
                        this.dialog = false
                        this.$msgbox({
                            title: '成功发起任务',
                            message: `<div><img style="height:200px" src="img/tasks-example.png"/></div><p>已成功发起导出任务，您可以操作其他事务</p><p>稍后可在 <b>任务中心</b> 查看执行结果</p>`,
                            type: 'success',
                            confirmButtonText: '知道了',
                            dangerouslyUseHTMLString: true,
                            center: true
                        }).catch(() => {})
                    } else {
                        this.$alert(res.message || '未知错误', '发起任务失败', {
                            type: 'error',
                            center: true
                        }).catch(() => {})
                    }
                })
                .catch(() => {
                    this.asyncLoading = false
                })
        },
        toQueryString(obj) {
            let arr = []
            for (var k in obj) {
                arr.push(`${k}=${obj[k]}`)
            }
            return (arr.length > 0 ? '?' : '') + arr.join('&')
        },
        handleDynamicColumns(dynamicColumns, tableData) {
            dynamicColumns.forEach((column) => {
                tableData.forEach((userData) => {
                    userData[column.key] &&
                        userData[column.key].forEach((data) => {
                            let assignData = {}
                            for (let tmp of Object.entries(data)) {
                                assignData[data.id + tmp[0]] = tmp[1]
                            }
                            Object.assign(userData, assignData)
                        })
                })
            })
        },
        formatJson(filterVal, jsonData) {
            // console.log(999999999, filterVal, jsonData)
            return jsonData.map((v) =>
                filterVal.map((j) => {
                    switch (j) {
                        case 'bill_tp':
                            if (v[j] == 'dev') return '刷脸就餐'
                            if (v[j] == 'baocan') return '手机报餐'
                            if (v[j] == 'waisong') return '外送订单'
                            if (v[j] == 'baocan_over') return '报餐核销'
                            if (v[j] == 'diancan') return '点餐未消费'
                            if (v[j] == 'diancan_over') return '手机点餐'
                            break
                        case 'cost_tp':
                            if (v[j] == 'cost') return '成功'
                            break
                        case 'dishess':
                            if (v[j]) {
                                const temObj = JSON.parse(v[j])
                                let temObjNew = ''
                                const switchName = {
                                    title: '名称',
                                    category_title: '分类',
                                    count: '数量',
                                    price: '价格'
                                }
                                if (Array.isArray(temObj)) {
                                    temObj.forEach((it) => {
                                        let obj = ''
                                        for (let i in switchName) {
                                            let name = switchName[i]
                                            // obj[name] = it[i]
                                            if (obj) {
                                                obj = obj + ',' + name + ':' + it[i]
                                            } else {
                                                obj = obj + name + ':' + it[i]
                                            }
                                        }
                                        if (temObjNew) {
                                            temObjNew = temObjNew + ';\n' + obj
                                        } else {
                                            temObjNew = temObjNew + obj
                                        }
                                    })
                                    return temObjNew
                                } else {
                                    console.log('-------519---3---')
                                    console.log(temObj)
                                }
                            }
                            break
                        case 'vehicle_condition':
                            if (v[j] == '0') return '车辆正常'
                            if (v[j] == '1') return '车辆异常'
                            break
                        case 'type':
                            if (filterVal.includes('subsidy_titles')) {
                                return v[j]
                            } else {
                                if (v[j] == '0') return '驶入'
                                if (v[j] == '1') return '驶出'
                            }
                            break
                        case 'timeStatusShow':
                            if (
                                new Date(v.start_time).getTime() < new Date().getTime() &&
                                new Date(v.end_time).getTime() >= new Date().getTime()
                            )
                                return '会议中'
                            if (new Date(v.start_time).getTime() > new Date().getTime()) return '待使用'
                            if (new Date(v.end_time).getTime() < new Date().getTime()) return '已结束'
                            break
                        case 'subsidy_titles':
                            v[j] = JSON.parse(v.subsidys)
                            v[j] = v[j] ? v[j].map((el) => el.title) : '未选择'
                            return v[j]
                            break
                        case 'repast_titles':
                            v[j] = JSON.parse(v.repasts)
                            v[j] = v[j] ? v[j].map((el) => el.title) : '未选择'
                            return v[j]
                            break
                        default:
                            return v[j]
                    }
                })
            )
        }
    }
}
</script>

<style scoped lang="scss">
.exportPdf {
    // display: none;
    position: absolute;
    // top: 500px;
    left: 0;
    z-index: -999;

    .table {
        border-collapse: collapse;
        // position: relative;
    }
}
</style>
