<!--
 * @author: 吉慧雯
 * @name: 计划任务
 * @desc: 介绍
 * @LastEditTime: 2022-12-30 15:55:07
 * @FilePath: \eyc3_guard_pc/src/views/work/basic/task
-->
<template>
    <yp_list
        ref="table"
        :url="url"
        :columns="columns"
        :formitems="formitems"
        :buttonList="buttonList"
        :paging="paging"
        :post_data="postdata"
    >
    </yp_list>
</template>

<script>
export default {
    name: 'BasicPower',
    data() {
        return {
            paging: true,
            url: 'permission_group/get_ls',
            // postdata:{},
            columns: [
                {
                    label: '管理组名称',
                    prop: 'title'
                },
                {
                    label: '管理员',
                    prop: 'admin_names'
                },
                // {
                //     label: '权限节点',
                //     prop: 'actions',
                //     component: 'array',
                //     options: [
                //         {
                //             prop: 'title'
                //         }
                //     ]
                // },
                // {
                //     label: '状态',
                //     prop: 'lock',
                //     component: 'tag',
                //     options: [
                //         {
                //             label: '解锁',
                //             type: '0',
                //             mode: 'success'
                //         },
                //         {
                //             label: '锁定',
                //             type: '1',
                //             mode: 'danger'
                //         }
                //     ]
                // },
                {
                    label: '操作',
                    prop: 'action',
                    component: 'button',
                    options: [
                        // {
                        //     label: '解锁',
                        //     component: 'lock',
                        //     prop: 'lock',
                        //     type: 'warning',
                        //     options: {
                        //         label: '锁定',
                        //         message: '是否确认锁定?',
                        //         remote: {
                        //             api: `permission/modify`,
                        //             state: 'edit',
                        //             data: [
                        //                 {
                        //                     id: '$id',
                        //                     lock: '$lock',
                        //                     title: '$title',
                        //                     userlst: '$userlst'
                        //                 }
                        //             ]
                        //         },
                        //         items: [
                        //             {
                        //                 label: '锁定',
                        //                 type: '0'
                        //             },
                        //             {
                        //                 label: '解锁',
                        //                 type: '1'
                        //             }
                        //         ]
                        //     }
                        // },
                        {
                            label: '详情',
                            component: 'detail',
                            options: {
                                title: '权限管理详情',
                                remote: {
                                    api: `permission_group/get_info`,
                                    data: {
                                        id: '$id'
                                    }
                                },
                                items: [
                                    {
                                        label: '管理组名称',
                                        prop: 'title'
                                    },
                                    {
                                        label: '管理员',
                                        prop: 'userlst',
                                        component: 'objArr',
                                        options: {
                                            objName: 'users',
                                            arrName: 'name'
                                        }
                                    },
                                    {
                                        label: '权限节点',
                                        prop: 'actions',
                                        component: 'arrayObj',
                                        options: {
                                            key: 'title'
                                        }
                                    }
                                    // {
                                    //     label: '解锁状态',
                                    //     prop: 'lock',
                                    //     component: 'tag',
                                    //     options: {
                                    //         items: [
                                    //             {
                                    //                 label: '解锁',
                                    //                 value: 0
                                    //             },
                                    //             {
                                    //                 label: '锁定',
                                    //                 value: 1
                                    //             }
                                    //         ]
                                    //     }
                                    // }
                                ]
                            }
                        },
                        {
                            label: '修改',
                            component: 'form',
                            options: {
                                name: 'BasicPowerEdit', // 跳转页面名
                                remote: {
                                    state: 'edit', // 状态,'add'|'edit'|'detail'
                                    label: '编辑权限管理', // 页头名
                                    api: 'permission_group/get_info', // 获取详情接口
                                    edit: 'permission_group/post_modify', // 修改详情接口
                                    data: {
                                        // 获取详情接口数据
                                        id: '$id'
                                    }
                                }
                                // relate: [
                                //     {
                                //         prop: 'lock',
                                //         value: 1,
                                //         message: '已锁定,请解锁后操作'
                                //     }
                                // ]
                            }
                        },
                        {
                            label: '删除',
                            type: 'danger',
                            component: 'confirm',
                            options: {
                                label: '确认删除',
                                message: '是否确认删除?',
                                remote: {
                                    // api: `permission_group/del`,
                                    api: `permission_group/post_del`,
                                    data: {
                                        id: '$id'
                                    }
                                }
                                // relate: [
                                //     {
                                //         prop: 'lock',
                                //         value: 1,
                                //         message: '已锁定,请解锁后操作'
                                //     }
                                // ]
                            }
                        }
                    ]
                }
            ],
            // formitems: [
            //     {
            //         label: '管理组名称',
            //         name: 'title',
            //         component: 'input',
            //         options: {
            //             placeholder: '请输入'
            //         }
            //     }
            // ],
            buttonList: [
                {
                    label: '新建',
                    component: 'form',
                    options: {
                        name: 'BasicPowerAdd', // 跳转页面名
                        icon: 'el-icon-Plus', // 按钮图标
                        remote: {
                            state: 'add', // 状态
                            label: '新增权限管理', // 页头名
                            // api: 'permission_group/add', // 新增地址接口
                            api: 'permission_group/post_add', // 新增地址接口
                            data: {}
                        }
                    }
                }
            ]
        }
    },
    //路由跳转进来 判断from是否有特殊标识做特殊处理
    beforeRouteEnter(to, from, next) {
        next((vm) => {
            if (from.is) {
                //删除特殊标识，防止标签刷新重复执行
                delete from.is
                //执行特殊方法
                this.$refs.table.finishEvent()
            }
        })
    }
}
</script>

<style lang="scss" scoped>
</style>
