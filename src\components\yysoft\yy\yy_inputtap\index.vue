<template>
    <el-input
        type="textarea"
        placeholder="请输入内容"
        v-model="form.content"
        :maxlength="colums.options.maxlength"
        show-word-limit
        @input="inputvalue"
    >
    </el-input>
    <div class="tapcard">
        <div class="taptitle">可用标签</div>
        <div class="tap">
            <template
                v-for="(item, index) in tapitems"
                :key="index"
            >
                <el-tag
                    @click="changevalue(item.value)"
                    :effect="item.effect"
                    >{{ item.label }}</el-tag
                >
            </template>
        </div>
    </div>
</template>

<script>
export default {
    name: 'yy_inputtap',
    props: {
        // 表单信息
        form: { type: Object, default: () => {} },
        colums: { type: Object, default: () => {} }
    },
    data() {
        return {
            tapitems: []
        }
    },
    mounted() {
        this.tapitems = this.colums.options.item
        this.changetap()
    },
    methods: {
        // 改变 value 内容
        changevalue(value) {
            var ishave = this.form.content.indexOf(value)
            if (ishave >= 0) {
                this.form.content = this.form.content.split(value).join('')
            } else {
                var content = this.form.content + value
                if (content.length <= this.colums.options.maxlength) {
                    this.form.content = this.form.content + value
                } else {
                    this.$message.error(`字符长度不能超过${this.colums.options.maxlength}个`)
                }
            }
            this.changetap()
        },
        // 动态处理tap的effect
        changetap() {
            this.tapitems.forEach((item) => {
                var ishave = this.form.content.indexOf(item.value)
                if (ishave >= 0) {
                    item.effect = 'dark'
                } else {
                    item.effect = 'plain'
                }
            })
        },
        inputvalue(value) {
            this.changetap()
        }
    }
}
</script>

<style lang="scss" scoped>
.tapcard {
    display: flex;
    margin-top: 15px;
    .taptitle {
        width: 80px;
        color: rgb(107, 107, 107);
    }
}
</style>