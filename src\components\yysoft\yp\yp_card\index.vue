<template>
    <template v-if="isPower">
        <el-main class="yy-card">
            <el-row :gutter="15">
                <el-col :lg="24">
                    <template
                        v-for="(item, index) in this.columns"
                        :key="index"
                    >
                        <template v-if="item.components == 'poster'">
                            <el-card
                                shadow="never"
                                class="aboutTop"
                                :style="{ background: this.background }"
                                body-style="cardstyle"
                            >
                                <div
                                    ref="aboutTop"
                                    class="poster"
                                    :style="{ color: this.fontcolor }"
                                >
                                    <template
                                        v-for="(itm, inx) in item.items"
                                        :key="inx"
                                    >
                                        <template v-if="itm.components == 'app_logo'">
                                            <img
                                                class="app-logo"
                                                src="/img/logo.png"
                                            />
                                        </template>
                                        <template v-else-if="itm.components == 'title'">
                                            <p class="title">{{ itm.options.text }}</p>
                                        </template>
                                        <template v-else-if="itm.components == 'code'">
                                            <sc-qr-code
                                                style="margin-top: 18px"
                                                :text="this.form[itm.name]"
                                                :logo="itm.options.isimg ? '/img/logo.png' : ''"
                                            ></sc-qr-code>
                                            <template v-if="itm.options.message">
                                                <p>{{ itm.options.message }}</p>
                                            </template>
                                        </template>
                                        <template v-else>
                                            <p class="else">{{ this.form[itm.name] }}</p>
                                        </template>
                                    </template>
                                </div>
                            </el-card>
                        </template>
                        <template v-else-if="item.components == 'descriptions'">
                            <el-card shadow="never">
                                <el-descriptions
                                    style
                                    border
                                    :column="item.column ? item.column : 1"
                                >
                                    <template
                                        v-for="(value, key) in item.items"
                                        :key="key"
                                    >
                                        <template v-if="value.components == 'copy'">
                                            <el-descriptions-item
                                                :label="value.label"
                                                label-align="right"
                                                align="center"
                                                width="10px"
                                            >
                                                <div class="copy-box">
                                                    <p>{{ this.form[value.name] }}</p>
                                                    <el-button
                                                        class="copy-btn"
                                                        size="small"
                                                        v-copy="this.form[value.name]"
                                                        >复制</el-button
                                                    >
                                                </div>
                                            </el-descriptions-item>
                                        </template>
                                        <template v-else>
                                            <el-descriptions-item
                                                label-align="right"
                                                align="center"
                                                width="150px"
                                            >
                                                <template #label>
                                                    <p>{{ value.label }}</p>
                                                </template>
                                                <p>{{ this.form[value.name] }}</p>
                                            </el-descriptions-item>
                                        </template>
                                    </template>
                                </el-descriptions>
                            </el-card>
                        </template>
                        <template v-else-if="item.components == 'table'">
                            <el-card
                                class="box-card"
                                shadow="never"
                            >
                                <div class="table-label">
                                    <span>{{ item.label ? item.label : '' }}</span>
                                    <div
                                        v-for="(item, index) in item.buttonList"
                                        :key="index"
                                        class="button_div"
                                    >
                                        <yy_upload
                                            v-if="item.component == 'upload'"
                                            :label="item.label"
                                            :templateUrl="item.templateUrl"
                                            :columnData="item.columnData"
                                            :url="item.url"
                                            :maxSize="item.maxSize"
                                        ></yy_upload>
                                        <yy_button
                                            v-if="item.component == 'form'"
                                            :label="item.label"
                                            :type="item.type"
                                            :options="item.options"
                                            :value="query"
                                            :component="item.component"
                                        ></yy_button>
                                        <yy_button
                                            v-if="item.component == 'confirm'"
                                            :label="item.label"
                                            :type="item.type"
                                            :options="item.options"
                                            :value="query"
                                            :component="item.component"
                                            @finishEvent="finishEvent"
                                        ></yy_button>
                                    </div>
                                </div>
                                <el-divider></el-divider>
                                <yy_table
                                    :data="form[item.name]"
                                    :formitems="item.formitems"
                                    @finish-event="finishEvent"
                                ></yy_table>
                            </el-card>
                        </template>
                    </template>
                </el-col>
            </el-row>
        </el-main>
    </template>
    <template v-else>
        <el-empty description="暂无权限"></el-empty>
    </template>
</template>

<script>
import { ElMessage, ElNotification } from 'element-plus'
import useTabs from '@/utils/useTabs'

export default {
    name: 'yp_card',
    props: {
        url: { type: String, default: '' },
        postdata: { type: Object, default: () => {} },
        columns: { type: Object, default: () => {} }
    },
    data() {
        return {
            form: {},
            background: '#ffffff',
            fontcolor: '#000000',
            isPower: true
        }
    },
    mounted() {
        this.getData()
        // 设置背景色
        this.columns.forEach((el) => {
            if (el.background) {
                this.background = el.background
                this.fontcolor = '#ffffff'
            }
        })
    },
    methods: {
        getData() {
            const loading = this.$loading({
                lock: true,
                text: 'Loading'
                // spinner: 'el-icon-loading',
                // background: 'rgba(0, 0, 0, 0.7)'
            })
            this.$HTTP
                .get(this.url)
                .then((res) => {
                    if (res.errcode != 0) {
                        if (res.errcode == 510) {
                            this.isPower = false
                        } else {
                            ElMessage.error(res.errmsg)
                        }
                    } else {
                        this.form = res.result
                    }
                })
                .finally(() => {
                    loading.close()
                })
        },
        finishEvent() {
            // this.getData()
            // console.log('-----')
            useTabs.refresh()
        }
    }
}
</script>

<style scoped lang="scss">
.aboutTop {
    .poster {
        text-align: center;

        .app-logo {
            width: 100px;
        }

        p {
            margin: 10px 0;
        }

        .title {
            font-size: 24px;
        }
    }
}

.copy-box {
    position: relative;
    display: flex;
    justify-content: center;

    .copy-btn {
        position: absolute;
        right: 0;
    }
}

.table-label {
    // display: flex;
    font-size: 22px;
    margin: 5px 0;

    .button_div {
        margin-top: 15px;
    }
}
</style>
