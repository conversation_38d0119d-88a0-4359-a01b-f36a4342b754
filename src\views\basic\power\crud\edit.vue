
<template>
    <yp_form
        :columns="columns"
        :backrouter="backrouter"
    ></yp_form>
</template>

<script>
export default {
    name: 'BasicPowerAdd',
    data() {
        return {
            backrouter: '/basic/power',
            columns: [
                {
                    label: '管理组名称',
                    name: 'title',
                    component: 'input',
                    options: {
                        placeholder: '请输入'
                    },
                    rules: [
                        {
                            required: true,
                            message: '请设置管理组名称',
                            trigger: 'change'
                        }
                    ]
                },
                {
                    label: '添加管理员',
                    name: 'userlst',
                    component: 'selectUser',
                    options: {
                        type: 'edit'
                    },
                    rules: [
                        {
                            required: true,
                            message: '请设置管理员',
                            trigger: 'change'
                        }
                    ],
                    message: '不支持部门授权,请选择人员'
                },
                // {
                //     label: '管理范围',
                //     name: 'manage_scope',
                //     component: 'select',
                //     options: {
                //         items: [
                //             {
                //                 label: '全组织',
                //                 value: 0
                //             },
                //             {
                //                 label: '所在部门和下级部门',
                //                 value: 1
                //             },
                //             {
                //                 label: '所在部门',
                //                 value: 2
                //             }
                //         ]
                //     }
                // },
                // {
                //     label: '分配权限',
                //     name: 'actions',
                //     component: 'checkboxlimits',
                //     value: '',
                //     type: 'edit',
                //     options: {
                //         remote: {
                //             api: `permission/get_all`
                //         }
                //     },
                //     rules: [
                //         {
                //             required: true,
                //             message: '请设置分配权限',
                //             trigger: 'change'
                //         }
                //     ]
                // }
                {
                    label: '分配权限',
                    name: 'actions',
                    component: 'powerTreeCheck',
                    options: {
                        butlabel: '选择权限',
                        remote: {
                            api: `permission_action/get_all`
                        },
                        childrenProp: 'acts',
                        name: 'title'
                    }
                }
            ]
        }
    }
}
</script>

<style>
</style>