<template>
    <div class="box">
        <el-button
            style="width: 100px"
            @click="openDialog"
        >
            {{ label }}
        </el-button>
        <div class="tag">
            <!-- {{ this.formDataTem }} -->
            <template
                v-for="(item, index) in this.formDataTem"
                :key="index"
            >
                <div class="tag-item">
                    <el-tag>{{ item[this.name] }}</el-tag>
                </div>
            </template>
        </div>
    </div>

    <el-dialog
        class="el-dialog"
        v-model="dialogVisible"
        :title="label"
        width="40%"
        :before-close="handleClose"
    >
        <el-checkbox
            :indeterminate="isIndeterminate"
            v-model="checkAll"
            @change="handleCheckAllChange"
            >全选</el-checkbox
        >
        <div class="checkbox">
            <div
                v-for="(item, index) in this.selectdata"
                :key="index"
                class="power-item"
            >
                <el-checkbox
                    v-model="item.checkAll"
                    :indeterminate="item.isIndeterminate"
                    @change="handleCheckedChangeOne(item)"
                    >{{ item[this.name] }}</el-checkbox
                >
                <el-checkbox-group
                    v-model="item.postData"
                    @change="handleCheckedChangeTwo(val, item)"
                >
                    <el-checkbox
                        v-for="(it, inx) in item[this.childrenProp]"
                        :key="inx"
                        :label="it"
                        >{{ it[this.name] }}
                    </el-checkbox>
                </el-checkbox-group>
            </div>
        </div>
        <div class="btn">
            <el-button
                class="query_btn"
                type="primary"
                @click="goSubmit"
                >提交</el-button
            >
        </div>
    </el-dialog>
</template>

<script>
import { ElMessageBox } from 'element-plus'
export default {
    name: 'yy_treeDialog',
    data() {
        return {
            checkAll: false,
            isIndeterminate: false,
            dialogVisible: false,
            selectdata: [],
            checkedCities: [],
            postData: [],
            formDataTem: [],
            temCheckOne: []
        }
    },
    props: {
        model: { type: String, default: '' },
        label: { type: String, default: '' },
        childrenProp: { type: String, default: '' },
        names: { type: String, default: '' },
        name: { type: String, default: '' },
        remote: { type: Object, default: () => {} },
        defaultProps: { type: Object, default: () => {} },
        form: { type: Object, default: () => {} }
    },
    watch: {
        selectdata: {
            handler(val) {
                // console.log('selectdata数据实时监控 --->', val)
                let isCkeckAll = true
                let isCheckNoall = true
                val.forEach((el) => {
                    if (el.checkAll) {
                        isCheckNoall = false
                    } else {
                        isCkeckAll = false
                    }
                    if (el.isIndeterminate) {
                        isCheckNoall = false
                    }
                })
                // console.log('查看处理结果', isCkeckAll, isCheckNoall)
                if (isCkeckAll) {
                    this.checkAll = true
                    this.isIndeterminate = false
                } else if (isCheckNoall) {
                    this.checkAll = false
                    this.isIndeterminate = false
                } else {
                    this.checkAll = false
                    this.isIndeterminate = true
                }
            },
            deep: true
        },
        form: {
            handler(val) {
                // console.log('监听清空事件 --->', val[this.names])
                if (!val[this.names]) {
                    this.formDataTem = []
                }
            },
            deep: true
        }
    },
    mounted() {
        this.getData()
    },
    methods: {
        // 获取数据
        getData() {
            this.$HTTP.get(this.remote.api).then((res) => {
                if (res.errcode != 0) {
                    ElMessage.error(res.errmsg)
                } else {
                    // console.log(this.data)
                    var data = res.result
                    // 处理列表数据
                    data.forEach((el) => {
                        // 一级全选
                        el.checkAll = false
                        // 一级部分选
                        el.isIndeterminate = false
                        // 二级选项
                        el.postData = []
                        if (typeof el[this.childrenProp] == 'string') {
                            el[this.childrenProp] = JSON.parse(el[this.childrenProp])
                        }
                    })
                    // 赋值
                    this.selectdata = data
                    // form 数据处理
                    if (this.form[this.names].length >= 1) {
                        // 下方标签赋值
                        this.formDataTem = JSON.parse(this.form[this.names])
                        this.selectdata.forEach((value) => {
                            this.formDataTem.forEach((el) => {
                                // console.log(el)
                                if (value.id == el.id) {
                                    // console.log(value)
                                    // console.log(el)
                                    if (el[this.childrenProp].length == value[this.childrenProp].length) {
                                        // 全选
                                        value.checkAll = true
                                        value.isIndeterminate = false
                                        value.postData = value[this.childrenProp]
                                        //                 this.postData.push(value)
                                    } else {
                                        // 部分选
                                        value.checkAll = false
                                        value.isIndeterminate = true
                                        // console.log(el[this.childrenProp])
                                        // console.log(value[this.childrenProp])
                                        // value.postData = JSON.parse(JSON.stringify(el[this.childrenProp]))
                                        value[this.childrenProp].forEach((j) => {
                                            el[this.childrenProp].forEach((k) => {
                                                if (JSON.stringify(j) == JSON.stringify(k)) {
                                                    value.postData.push(j)
                                                }
                                            })
                                        })
                                    }
                                }
                            })
                        })
                    }
                }
            })
        },
        // 全选
        handleCheckAllChange(val) {
            if (this.checkAll) {
                this.isIndeterminate = false
                this.selectdata.forEach((el) => {
                    el.checkAll = true
                    el.isIndeterminate = false
                    el.postData = el.acts
                })
            } else {
                this.isIndeterminate = false
                this.selectdata.forEach((el) => {
                    el.checkAll = false
                    el.isIndeterminate = false
                    el.postData = []
                })
            }
        },
        // 一级菜单变化
        handleCheckedChangeOne(val) {
            // console.log('一级菜单变化值 --->', val)
            val.isIndeterminate = false
            if (val.checkAll) {
                // 全选
                val.postData = val.acts
            } else {
                // 全不选
                val.postData = []
            }
        },
        // 二级菜单变化
        handleCheckedChangeTwo(val, item) {
            // console.log('选择二级选项变化 --->', val, item)
            if (item.postData.length == item[this.childrenProp].length) {
                // 全选
                item.isIndeterminate = false
                item.checkAll = true
            } else if (item.postData.length == 0) {
                // 全不选
                item.isIndeterminate = false
                item.checkAll = false
            } else {
                // 部分选
                item.isIndeterminate = true
                item.checkAll = false
            }
        },
        // 确定
        goSubmit() {
            // console.log('确定-----')
            // console.log(this.data)
            this.dialogVisible = false
            var formData = JSON.parse(JSON.stringify(this.selectdata))
            this.formDataTem = []
            formData.forEach((value) => {
                if (value.postData.length > 0) {
                    this.formDataTem.push(value)
                }
            })
            console.log('查看最后数据 --->', this.formDataTem)
            this.handlerData(this.formDataTem)
        },
        // 处理发送数据
        handlerData(val) {
            var temdata = JSON.parse(JSON.stringify(val))
            temdata.forEach((el) => {
                el[this.childrenProp] = el.postData
            })
            // console.log(this.form)
            this.form[this.names] = JSON.stringify(temdata)
        },
        // 返回
        goBack() {
            this.dialogVisible = false
        },
        // 打开遮罩层
        openDialog() {
            this.dialogVisible = true
            // this.isIndeterminate = false
            // this.temCheckOne = JSON.parse(JSON.stringify(this.postData))
            this.getData()
        }
    }
}
</script>

<style lang="scss" scoped>
.box {
    display: flex;
    flex-direction: column;

    .tag {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
        .tag-item {
            margin-right: 20px;
            //     width: 100px;
        }
    }
}

.power-item {
    border-bottom: 1px solid #dcdfe6;
}

.el-dialog {
    height: 40%;

    // .checkbox {
    //     height: 600px;
    //     overflow: auto;

    //     .divider {
    //         width: 80%;
    //         height: 1px;
    //         background-color: rgb(218, 218, 218);
    //     }
    // }

    .btn {
        margin-top: 20px;
    }
}
</style>