<template>
    <el-table
        style="width: 100%"
        :data="data"
        border
        :height="height == 'auto' ? null : '100%'"
        :stripe="true"
    >
        <template
            v-for="(item, index) in formitems"
            :key="index"
        >
            <el-table-column
                :label="item.label"
                :prop="item.prop"
            >
                <template #default="scope">
                    <tableslot
                        :item="item"
                        :scope="scope"
                        @finish-event="finishEvent"
                    ></tableslot>
                </template>
            </el-table-column>
        </template>
    </el-table>
</template>

<script>
/* eslint-disable */
import tableslot from './tableslot'
export default {
    name: 'yy_table',
    emits: ['finish-event'],
    components: {
        tableslot
    },
    props: {
        data: { type: Object, default: () => {} },
        formitems: { type: Object, default: () => {} },
        height: { type: [String, Number], default: '100%' }
    },
    mounted() {
        // this.fixElTableErr(Table)
    },
    methods: {
        finishEvent() {
            this.$emit('finish-event', true)
        },
        fixElTableErr(table) {
            const oldResizeListener = table.methods.resizeListener
            table.methods.resizeListener = function () {
                window.requestAnimationFrame(oldResizeListener.bind(this))
            }
        }
    }
}
</script>

<style>
</style>