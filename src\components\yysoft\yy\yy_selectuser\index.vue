<!--
 * @author: 风源
 * @name: 类名
 * @desc: 介绍
 * @LastEditTime: 2023-01-05 16:46:21
 * @FilePath: \eyc3_canyin_pc\src\components\yysoft\yy\yy_selectuser\index.vue
-->
<template>
    <div class="content">
        <div class="user-box">
            <el-input
                v-model="value"
                style="display: none"
            />
            <el-input
                class="el-input"
                v-model="textview"
                :placeholder="placeholder"
                :disabled="disabled"
                @click="onSelect"
                readonly
            >
            </el-input>
            <el-icon
                class="el-icon el-dialog__close"
                @click="cleanUser"
                ><el-icon-close
            /></el-icon>
        </div>
        <div
            class="recommend"
            v-if="isRecommend && recommendForm.userid"
        >
            <div class="title">推荐人员</div>
            <div class="re-card">
                <img
                    :src="recommendForm.avatar"
                    alt=""
                />
                <span>{{ recommendForm.name }}</span>
                <el-button
                    class="button"
                    type="primary"
                    plain
                    size="small"
                    >使用</el-button
                >
            </div>
        </div>
    </div>
    <!-- {{ recommendOptions }} -->
</template>

<script>
export default {
    name: 'yy_selectuser',
    props: {
        model: { type: String, default: '' },
        multiple: { type: Boolean, default: false },
        placeholder: { type: String, default: '' },
        items: { type: Object, default: () => null },
        query: { type: Boolean, default: false },
        isRecommend: { type: Boolean, default: false },
        recommendOptions: { type: Object, default: () => {} }
    },
    data() {
        return {
            remoteitems: {},
            loading: false,
            value: this.value,
            postData: {},
            textview: '',
            users: [],
            departments: [],
            selectUsers: [],
            disabled: false,
            objdata: {},
            recommendForm: {
                // name: '未命名',
                // avatar: 'http://files.qixuw.com/dingb9614df94342f570a1320dcb25e91351/huashan/2023-08-21/ZqUTPLeKVj053jxIB87RozxC5HIXxTD0.jpg'
            }
        }
    },
    created() {
        // console.log(this.items,'1231231231');
        if (this.items && this.items.type == 'info') {
            this.objdata = JSON.parse(this.model)
            this.disabled = true
            if (this.objdata) {
                this.textview = `已选择${this.objdata.departments.length}部门,${this.objdata.users.length}人员`
            }
        } else if (this.items && this.items.type == 'edit') {
            // console.log('yy_selectuser 组件', this.model)
            if (this.model) {
                this.objdata = JSON.parse(this.model)
                this.disabled = false
                if (this.objdata) {
                    if (this.objdata.departments && this.objdata.users) {
                        this.textview = `已选择${this.objdata.departments.length}部门,${this.objdata.users.length}人员`
                        this.selectUsers = this.objdata.users.map((item) => item.userid)
                    }
                }
            }
        }
        this.getRemmend()
    },
    watch: {
        modelValue(val) {
            this.value = val
        },
        value(val) {
            this.$emit('update:modelValue', val)
        },
        model(val) {
            // console.log('model', val)
            if (!val) {
                this.users = []
                this.selectDeparments = []
                this.textview = ''
            }
        }
    },
    methods: {
        onSelect() {
            var that = this
            if (this.users.length >= 1) {
                this.selectUsers = this.users.map((item) => item.emplId)
            }
            // var selectDeparments = this.departments.map((item) => item.id)
            var selectDeparments = []
            this.$TOOL.runtime.biz.contact.complexPicker({
                multiple: true, //是否多选：true多选 false单选； 默认true
                // users: ['10001', '10002', ...], //默认选中的用户列表，员工userid；成功回调中应包含该信息
                corpId: this.$TOOL.data.get('corpid'), //企业id
                pickedUsers: this.selectUsers,
                pickedDepartments: selectDeparments,
                appId: this.$TOOL.data.get('agentid'),
                maxUsers: 10000, //人数限制，当multiple为true才生效，可选范围1-1500
                onSuccess: function (data) {
                    that.users = data.users
                    that.departments = data.departments
                    // console.log('走到这里了么')
                    // console.log(data.departments)
                    data.departments = data.departments.map((it) => {
                        return {
                            dept_id: it.id,
                            name: it.name
                        }
                    })
                    data.users = data.users.map((it) => {
                        return {
                            userid: it.emplId,
                            name: it.name,
                            avatar: it.avatar
                        }
                    })
                    that.value = JSON.stringify(data)
                    that.textview = `已选择${that.departments.length}部门,${that.users.length}人员`
                },
                onFail: function (err) {
                    console.log(err)
                }
            })
        },
        cleanUser() {
            this.textview = ''
            this.selectUsers = []
            this.users = []
            if (this.query) {
                this.value = ''
            } else {
                var temValue = { departments: [], users: [] }
                this.value = JSON.stringify(temValue)
            }
        },
        // 获取推荐人员
        getRemmend() {
            // console.log('1111111', this.recommendOptions.post)
            if (this.isRecommend) {
                this.$HTTP.get(this.recommendOptions.api, this.recommendOptions.post).then((res) => {
                    console.log('推荐人员结果', res)
                    if (res.result.userid) {
                        this.recommendForm = res.result
                    }
                })
            }
        }
    }
}
</script>

<style scoped lang="scss">
// .content {
//     display: flex;
// }
.user-box {
    position: relative;

    .el-icon {
        position: absolute;
        top: 50%;
        right: 5px;
        margin-top: -7px;
        border: 1px solid #757575;
        border-radius: 50%;
        color: #757575;
        width: 14px;
        height: 14px;
        cursor: pointer;
        z-index: 999;
        opacity: 0;
    }
}

.user-box:hover {
    .el-icon {
        opacity: 1;
    }
}

.recommend {
    // margin-left: 16px;

    margin: 16px 0;
    .re-card {
        margin-top: 8px;
        display: flex;
        align-content: center;
        img {
            width: 28px;
            height: 28px;
            border-radius: 6px;
        }

        span {
            margin-left: 6px;
            // line-height: 33px;
        }

        .button {
            margin-left: 80px;
        }
    }
}
</style>