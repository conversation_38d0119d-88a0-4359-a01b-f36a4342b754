<!--
 * @author: 风源
 * @name: 类名
 * @desc: 介绍
 * @LastEditTime: 2022-12-11 00:14:05
 * @FilePath: \eyc3_pc_base\src\views\report\user\dininghall.vue
-->
<template>
	<yp_list
		ref="table"
		:url="url"
		row-key="id"
		:columns="columns"
		stripe
		:add="add"
		:derive="derive"
		:formitems="formitems"
	>
	</yp_list>
</template>

<script>
export default {
	name: "ReportUserDininghall",
	data() {
		return {
			url: "report/get_dininghall_sum",
			columns: [
				{
					label: "姓名",
					prop: "name",
				},
				{
					label: "部门名称",
					prop: "department_name",
				},

				{
					label: "订单总数",
					prop: "count",
				},
				{
					label: "总报餐数",
					prop: "apply_count",
				},
				{
					label: "总点餐数",
					prop: "order_count",
				},
				{
					label: "总消费金额",
					prop: "money",
				},
				{
					label: "总餐补",
					prop: "subsidy_money",
				},

				{
					label: "总优惠",
					prop: "coupon_money",
				},
				{
					label: "虚拟支付总金额",
					prop: "virtual_money",
				},
				{
					label: "真实支付总金额",
					prop: "real_money",
				},				
			],
			derive: {
				filename:"餐厅账单记录"
			},
			formitems: [
				{
					label: "日期范围",
					name: "date_interval",
					value: [
						this.$TOOL.dateFormat(new Date(), "yyyy-MM-01"),
						this.$TOOL.dateFormat(new Date(), "yyyy-MM-dd"),
					],
					component: "date",
					options: {
						type: "daterange",
						rangeseparator: "至",
						startplaceholder: "开始日期",
						endplaceholder: "结束日期",
						valueFormat: "YYYY-MM-DD",
					},
					rules: [
						{
							required: true,
							message: "Please input Data",
							trigger: "change",
						},
					],
				},
				{
					label: "选择人员",
					name: "userlst",
					value: "",
					component: "selectUser"
				},
				{
					label: "餐厅",
					name: "dininghall_id",
					value: "",
					component: "select",
					options: {
						remote: {
							api: `dininghall/get_all`,
							data: { name: "b" },
							label: "title",
							value: "id",
						},
						items: [
							{
								label: "全部",
								value: "",
							},
						],
					},
				},				
				{
					label: "离职人员",
					name: "isleave",
					value: false,
					component: "switch",
				},
				
			],
		};
	},
};
</script>

<style></style>
