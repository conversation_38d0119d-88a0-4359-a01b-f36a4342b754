<!--
 * @author: 吉慧雯
 * @name: 计划任务
 * @desc: 介绍
 * @LastEditTime: 2022-12-30 15:55:07
 * @FilePath: \eyc3_guard_pc/src/views/work/basic/plan
-->
<template>
    <yp_list
        ref="table"
        :url="url"
        :post_data="postData"
        :columns="columns"
        :formitems="formitems"
        :buttonList="buttonList"
        @info-data="infodata"
    >
        <template #descriptions-footer>
            <el-main class="info" v-loading="loading">
                <div class="content">
                    <div class="basics">
                        <div class="title">基础</div>
                        <el-divider />
                        <div class="btns">
                            <el-form
                                :model="infodatas"
                                label-width="120px"
                                label-position="top"
                            >
                                <el-row>
                                    <el-col :span="8">
                                        <el-form-item label="人员">
                                            {{ infodatas.names }}
                                        </el-form-item>
                                    </el-col>
                                    <el-col
                                        :span="8"
                                        :offset="3"
                                    >
                                        <el-form-item label="任务分组">
                                            {{ infodatas.group_title }}
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-form-item label="任务时间">
                                    <template v-if="infodatas.node_info.duration!=null&&!infodatas.node_info.begin_time">
                                       已填报工时：{{ infodatas.node_info.duration }}天
                                    </template>
                                    <template v-if="infodatas.node_info.begin_time">
                                        {{ infodatas.node_info.begin_time }} -- {{ infodatas.node_info.end_time }}
                                    </template>
                                    <template v-if="infodatas.node_info.duration==null">
                                        <el-button @click="manHour">请填报工时</el-button>
                                    </template>
                                </el-form-item>
                            </el-form>
                        </div>
                    </div>
                    <div class="task">
                        <div class="title">任务内容</div>
                        <el-divider />
                        <div class="stitle">{{ infodatas.title }}</div>
                        <div class="desc">
                            {{ infodatas.content }}
                        </div>
                        <div
                            class="accessory"
                            v-if="infodatas.file"
                        >
                            <div class="stitle">任务附件</div>
                            <template
                                v-for="(el, i) in JSON.parse(infodatas.file)"
                                :key="i"
                            >
                                <div class="file">
                                    <img
                                        :src="el.url"
                                        alt=""
                                        @click="openFile(el.url)"
                                    />
                                    <div
                                        class="name"
                                        @click="openFile(el.url)"
                                    >
                                        {{ el.name }}
                                    </div>

                                    <div
                                        class="download"
                                        @click="download(el)"
                                    >
                                        下载
                                    </div>
                                </div>
                            </template>
                        </div>
                        <div class="flow">
                            <div class="stitle">任务流程</div>
                            <el-timeline>
                                <el-timeline-item
                                    v-for="(activity, index) in infodatas.process"
                                    :key="index"
                                    :timestamp="activity.created_at"
                                >
                                    {{ activity.user_name }}{{ activity.action }}
                                </el-timeline-item>
                            </el-timeline>
                        </div>
                    </div>
                </div>
                <div class="footer">
                    <termplate v-if="infodatas.status == 0">
                        <template v-if="infodatas.node_info.duration">
                            <el-button
                                type="primary"
                                round
                                icon="el-icon-edit-pen"
                                @click="completeTask"
                                >每日提报</el-button
                            >
                        </template>
                        <template v-else>
                            <el-button round>未开始</el-button>
                        </template>
                    </termplate>
                    <!-- <el-button round>等待验收</el-button> -->
                    <termplate v-if="infodatas.status == 1">
                        <el-button
                            type="primary"
                            round
                            icon="el-icon-check"
                            @click="check(0)"
                            >验收通过</el-button
                        >
                        <el-button
                            type="danger"
                            round
                            icon="el-icon-close"
                            @click="check(1)"
                            >未达标</el-button
                        >
                    </termplate>
                    <termplate v-if="infodatas.status == 2">
                        <el-button round>我已完成</el-button>
                    </termplate>
                </div>
            </el-main>
        </template>
    </yp_list>
</template>

<script>
import useTabs from '@/utils/useTabs'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
export default {
    name: 'BasicPlan',
    data() {
        return {
            loading:false,
            url: 'task/get_my_ls',
            postData: {
                type: this.$route.query.type
            },
            columns: [
                {
                    label: '任务名称',
                    prop: 'task_title'
                },
                {
                    label: '人员',
                    prop: 'user_name'
                },
                {
                    label: '任务分组',
                    prop: 'group_title'
                },
                {
                    label: '开始时间',
                    prop: 'begin_time'
                },
                {
                    label: '结束时间',
                    prop: 'end_time'
                },
                {
                    label: '操作',
                    prop: 'action',
                    component: 'button',
                    options: [
                        {
                            label: '详情',
                            component: 'detail',
                            options: {
                                title: '任务详情',
                                remote: {
                                    api: 'task/get_my_info', // 获取详情接口
                                    data: {
                                        // 获取详情接口数据
                                        id: '$task_id',
                                        node_id: '$id'
                                    }
                                },
                                haveslot: true
                            }
                        }
                    ]
                }
            ],
            infodatas: {
                node_info: {}
            },
            resourceName: '资源不存在',
            loadingComplete: false
        }
    },
    methods: {
        infodata(infodata) {
            console.log(infodata,666)
            this.infodatas = JSON.parse(infodata)
            if (this.infodatas.file) {
                this.infodatas.files = JSON.parse(this.infodatas.file)
            }
            this.infodatas.names = `${this.infodatas.flow.title}，${this.infodatas.flow.finish}/${this.infodatas.flow.total}已完成`
        },
        manHour() {
            this.$prompt('请填写该项目工时', '工作时长', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                inputPlaceholder: '请输入时长（天）'
            })
                .then(({ value }) => {
                    // this.loading = true
                    this.$HTTP
                        .get('task/post_modify_time', {
                            id: this.infodatas.node_info.id,
                            task_id: this.infodatas.id,
                            duration: value
                        })
                        .then((res) => {
                            if (res.errcode == 0) {
                                ElMessage.success('提交成功')
                                useTabs.refresh()
                            } else {
                                ElMessage.error(res.errmsg)
                            }
                            // this.loading = false
                        })
                        .finally(() => {})
                })
                .catch(() => {})
        },
        finishTask() {
            this.$HTTP
                .post('task/post_finish', {
                    id: this.infodatas.id
                })
                .then((res) => {
                    if (res.errcode == 0) {
                        ElMessage.success('提交成功')
                        this.infodatas.status = 0
                        // useTabs.close()
                    } else {
                        ElMessage.error(res.errmsg)
                    }
                })
                .finally(() => {})
        },
        // 预览文件
        openFile(url) {
            window.open(url)
        },
        // 下载文件
        download(el) {
            this.$TOOL.runtime.biz.util.downloadFile({
                url: el.url, //要下载的文件的url
                name: el.name, //定义下载文件名字
                onProgress: function (msg) {
                    // 文件下载进度回调
                },
                onSuccess: function (result) {
                    /*
          true
        */
                },
                onFail: function () {}
            })
        },

        // 验收任务
        check(type) {
            this.loading = true
            // console.log(this.infodatas)
            this.$HTTP
                .post('task/post_confirm', {
                    id: this.infodatas.id,
                    node_id: this.infodatas.node_info.id,
                    type
                })
                .then((res) => {
                    if (res.errcode == 0) {
                        ElMessage.success('提交成功')
                        // this.getCheckList()
                             // 关闭当前对话框
                             useTabs.refresh()
                        // 刷新父组件列表
                        // this.$parent.$refs.table.refresh()
                    } else {
                        ElMessage.error(res.errmsg)
                    } 
                    this.loading = false
                })
                .finally(() => {})
        },
        // 完成任务
        completeTask() {
            this.loadingComplete = true
            this.$prompt('每日提报', '请填写当前任务完成百分比', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                inputPlaceholder: '%'
            })
                .then(({ value }) => {
                    // alert(value)
                    this.$HTTP
                        .post('task/post_complete', {
                            id: this.infodatas.node_info.id
                        })
                        .then((res) => {
                            if (res.errcode == 0) {
                                ElMessage.success('提交成功')
                                useTabs.refresh()
                            } else {
                                ElMessage.error(res.errmsg)
                            }
                            this.loadingComplete = false
                        })
                        .finally(() => {})
                })
                .catch(() => {})
        }
    }
}
</script>

<style lang="scss" scoped>
.info {
    position: relative;
    height: 90vh;
    width: 100%;
    overflow: hidden;
}

.content {
    height: 100vh;
    box-sizing: border-box;
    overflow: scroll;
    padding-bottom: 200px;
}

.basics {
    // padding: 0 20px;
    width: 100%;

    .title {
        // margin-bottom: 20px;
        font-size: 16px;
        font-weight: 550;
    }

    .btns {
        // margin-top: 30px;

        // .item {
        //     display: flex;
        //     padding: 9px 16px;
        //     margin-bottom: 12px;
        //     width: 40%;
        //     line-height: 16px;
        //     border-radius: 17px;
        //     border: 1px solid #eaebed;
        // }
    }
}

.task {
    .title {
        // margin-bottom: 20px;
        font-size: 16px;
        font-weight: 550;
    }

    .stitle {
        margin-bottom: 16px;
        font-size: 16px;
    }

    .accessory {
        margin-top: 30px;

        .file {
            position: relative;
            display: flex;
            align-content: center;
            margin-top: 12px;
            background-color: #ebf5ff;
            padding: 8px;
            width: 60%;
            line-height: 45px;
            border-radius: 8px;

            img {
                margin-right: 8px;
                width: 45px;
                height: 45px;
                border-radius: 4px;
            }

            .name {
                width: 80%;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .download {
                position: absolute;
                right: 4px;
                color: #409eff;
                cursor: pointer;
            }
        }
    }

    .flow {
        margin-top: 20px;
    }
}

.footer {
    background-color: #fff;
    padding-top: 20px;
    padding-left: 16px;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    border-top: 1px solid #dcdfe6;
}
</style>
