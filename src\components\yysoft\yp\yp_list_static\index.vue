<!--
 * @Descripttion: 数据表格组件
 * @version: 1.10
 * @Author: sakuya
 * @Date: 2021年11月29日21:51:15
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-01-07 13:22:02
-->

<template>
    <el-container>
        <el-header
            height="auto"
            style="display: block"
        >
            <yy_tab
                v-if="tabitems"
                :tabitems="tabitems"
                @queryformdata="queryformdata"
                :derive="derive"
            ></yy_tab>
            <yy_queryform
                v-if="formitems"
                v-model="queryform"
                label-width="120px"
                ref="queryform"
                :filed="formitems"
            >
                <div class="button_lst">
                    <div class="button_left">
                        <div
                            v-for="(item, index) in buttonList"
                            :key="index"
                            class="button_left_div"
                        >
                            <yy_upload
                                v-if="item.component == 'upload'"
                                :label="item.label"
                                :templateUrl="item.templateUrl"
                                :url="item.url"
                                :maxSize="item.maxSize"
                            ></yy_upload>
                            <yy_button
                                v-if="item.component == 'form'"
                                :label="item.label"
                                :type="item.type"
                                :options="item.options"
                                :value="query"
                                :component="item.component"
                            ></yy_button>
                            <yy_button
                                v-if="item.component == 'confirm'"
                                :label="item.label"
                                :type="item.type"
                                :options="item.options"
                                :value="query"
                                :component="item.component"
                                @finishEvent="finishEvent"
                            ></yy_button>
                        </div>
                        <!-- <el-button
                            type="primary"
                            icon="el-icon-plus"
                            >新建</el-button
                        >  -->
                        <!-- <el-button 
                            type="danger"
                            plain
                            icon="el-icon-delete"
                            v-if="del"
                            >删除</el-button
                        > -->
                    </div>
                    <div class="button-right">
                        <el-button
                            type="primary"
                            @click="refresh"
                            icon="el-icon-Search"
                            >查询</el-button
                        >
                        <el-button
                        v-if="resettingnode"
                        style="display: none;"
                            plain
                            @click="resetForm"
                            >重置</el-button
                        >
                        <el-button
                        v-else
                            plain
                            @click="resetForm"
                            >重置</el-button
                        >
                        <yy_export
                            v-if="derive"
                            :url="url"
                            :data="derive.data"
                            showData
                            :column="userColumn"
                            :dynamicColumns="dynamicColumns"
                            :fileTypes="['xlsx']"
                            :query="queryform"
                            :fileName="derive.filename"
                            :showsummary="showsummary"
                        >
                        </yy_export>
                    </div>
                </div>
            </yy_queryform>
        </el-header>
        <el-main class="nopadding">
            <div
                class="scTable"
                :style="{ height: _height }"
                ref="scTableMain"
                v-loading="loading"
            >
                <div
                    class="scTable-table"
                >
                <!-- 静态资源展示 -->
                    <div v-for="item in userclostatic" :key="item">
                        <el-table :data="item.children">
                            <el-table-column
                            v-for="{ prop, label } in colConfigs"
                            :key="prop"
                            :prop="prop"
                            :label="label">
                            </el-table-column>
                        </el-table>
                    </div>
                    <el-table
                        v-bind="$attrs"
                        :data="tableData"
                        :row-key="rowKey"
                        :key="toggleIndex"
                        ref="scTable"
                        :height="height == 'auto' ? null : '100%'"
                        :border="true"
                        :stripe="true"
                        @sort-change="sortChange"
                        @filter-change="filterChange"
                        :show-summary="showsummary"
                        id="yytable"
                    >
                        <!-- <el-table-column type="selection"></el-table-column> -->

                        <template
                            v-for="(item, index) in userColumn"
                            :key="index"
                        >
                            <el-table-column
                                v-if="item.children"
                                :label="item.label"
                                :width="item.width"
                                header-align="center"
                            >
                                <el-table-column
                                    v-for="(rs, rs_index) in item.children"
                                    :key="rs_index"
                                    :column-key="rs.prop"
                                    :label="rs.label"
                                    :prop="rs.prop"
                                    :width="rs.width"
                                    :sortable="rs.sortable"
                                    :fixed="rs.fixed"
                                    :filters="rs.filters"
                                    :filter-method="remoteFilter || !rs.filters ? null : filterHandler"
                                    :show-overflow-tooltip="rs.showOverflowTooltip"
                                >
                                    <template #default="scope">
                                        <tableslot
                                            :item="rs"
                                            :scope="scope"
                                            :queryform="queryform"
                                            @finish-event="finishEvent"
                                        ></tableslot>
                                    </template>
                                </el-table-column>
                            </el-table-column>
                            <el-table-column
                                v-if="!item.hide && !item.children"
                                :column-key="item.prop"
                                :label="item.label"
                                :prop="item.prop"
                                :width="item.width"
                                :sortable="item.sortable"
                                :fixed="item.fixed"
                                :filters="item.filters"
                                :filter-method="remoteFilter || !item.filters ? null : filterHandler"
                                :show-overflow-tooltip="item.showOverflowTooltip"
                            >
                                <template #default="scope">
                                    <tableslot
                                        :item="item"
                                        :scope="scope"
                                        :queryform="queryform"
                                        @finish-event="finishEvent"
                                    ></tableslot>
                                </template>
                            </el-table-column>
                        </template>
                        <el-table-column min-width="1"></el-table-column>
                        <template #empty>
                            <el-empty
                                :description="emptyText"
                                :image-size="100"
                            ></el-empty>
                        </template>
                    </el-table>
                </div>
                <div
                    class="scTable-page"
                    v-if="!hidePagination || !hideDo"
                >
                    <div class="scTable-pagination">
                        <el-pagination
                            v-if="!hidePagination && paging"
                            background
                            :small="true"
                            :layout="paginationLayout"
                            :total="total"
                            :page-size="query.per_page"
                            :page-sizes="pageSizes"
                            v-model:currentPage="query.page"
                            @current-change="paginationChange"
                            @update:page-size="pageSizeChange"
                        ></el-pagination>
                    </div>
                    <div
                        class="scTable-do"
                        v-if="!hideDo"
                    >
                        <el-button
                            v-if="!hideRefresh"
                            @click="refresh"
                            icon="el-icon-refresh"
                            circle
                            style="margin-left: 15px"
                        ></el-button>
                        <el-popover
                            v-if="userColumn"
                            placement="top"
                            title="列设置"
                            :width="500"
                            trigger="click"
                            :hide-after="0"
                            @show="customColumnShow = true"
                            @after-leave="customColumnShow = false"
                        >
                            <template #reference>
                                <el-button
                                    icon="el-icon-set-up"
                                    circle
                                    style="margin-left: 15px"
                                ></el-button>
                            </template>
                            <columnSetting
                                v-if="customColumnShow"
                                ref="columnSetting"
                                @userChange="columnSettingChange"
                                @save="columnSettingSave"
                                @back="columnSettingBack"
                                :column="userColumn"
                            ></columnSetting>
                        </el-popover>
                    </div>
                </div>
            </div>
        </el-main>
    </el-container>
</template>

<script>
import config from '@/config/table'
import columnSetting from './columnSetting'
import tableslot from './tableslot'
import { ElMessage, ElMessageBox, ElNotification, dayjs } from 'element-plus'
export default {
    name: 'yp_list',
    components: {
        columnSetting,
        tableslot
    },
    props: {
        add: { type: Object, default: () => null },
        del: { type: Object, default: () => null },
        derive: { type: Object, default: () => null },
        columns: { type: Object, default: () => {} },
        colstatis: { type: Object, default: () => {} },
        // dynamicColumns: { type: Array, default: () => [] },
        url: { type: String, default: '' },
        formitems: { type: Object, default: () => {} },
        paging: { type: Boolean, default: true },
        showsummary: { type: Boolean, default: false },
        resettingnode: { type: Boolean, default: false },
        buttonList: { type: Object, default: () => {} },
        post_data: { type: Object, default: () => {} },
        tabitems: { type: Object, default: () => {} },

        tableName: { type: String, default: '' },
        params: { type: Object, default: () => ({}) },
        data: { type: Object, default: () => {} },
        height: { type: [String, Number], default: '100%' },
        size: { type: String, default: 'default' },
        border: { type: Boolean, default: true },
        stripe: { type: Boolean, default: true },
        pageSize: { type: Number, default: config.pageSize },
        pageSizes: { type: Array, default: config.pageSizes },
        rowKey: { type: String, default: '' },
        remoteSort: { type: Boolean, default: false },
        remoteFilter: { type: Boolean, default: false },
        remoteSummary: { type: Boolean, default: false },
        hidePagination: { type: Boolean, default: false },
        hideDo: { type: Boolean, default: false },
        hideRefresh: { type: Boolean, default: false },
        hideSetting: { type: Boolean, default: false },
        paginationLayout: { type: String, default: config.paginationLayout }
    },
    watch: {
        //监听从props里拿到值了
        data() {
            this.tableData = this.data
            this.total = this.tableData.length
        },
        apiObj() {
            this.tableParams = this.params
            this.refresh()
        }
    },
    computed: {
        _height() {
            return Number(this.height) ? Number(this.height) + 'px' : this.height
        },
        _table_height() {
            return this.hidePagination && this.hideDo ? '100%' : 'calc(100% - 50px)'
        }
    },
    data() {
        return {
            userColumn: [],
            userclostatic: [],
            dynamicColumns: [],
            queryform: {},
            query: {
                download: 0,
                per_page: 10,
                page: 1
                // date_interval:"[\"2022-12-01\",\"2022-12-08\"]"
            },
            isInit: false,
            isActivat: true,
            emptyText: '暂无数据',
            toggleIndex: 0,
            tableData: [],
            total: 0,
            prop: null,
            order: null,
            loading: false,
            tableHeight: '100%',
            tableParams: this.params,
            customColumnShow: false,
            summary: {},
            config: {
                size: this.size,
                border: this.border,
                stripe: this.stripe
            },
            routeData: {},
            temporaryqueryform: []
        }
    },
    created() {
        console.log(this.colstatis);
        if(this.colstatis){
            this.userclostatic = this.colstatis
            this.userclostatic.forEach((res)=>{
                this.colConfigs = [{ prop: 'label', label: res.label }]
            })
        }
    },
    mounted() {
        //判断是否开启自定义列
        this.userColumn = JSON.parse(JSON.stringify(this.columns))
        // console.log(this.$route.params)
        if (this.$route.params) {
            this.routeData = this.$route.params
            // console.log('走了没')
            // console.log(this.routeData)
            if (this.formitems) {
                this.formitems.forEach((item) => {
                    if (item.options && item.options.showprop) {
                        if (this.routeData[item.options.showprop]) {
                            if (this.routeData[item.options.showprop] == item.options.showcontent) {
                                item.options.merge = false
                                this.queryform[item.name] = this.routeData[item.name]
                            }
                        }
                    }
                })
            }
        }
        setTimeout(() => {
            this.refresh()
        }, 100)
    },
    activated() {
        if (!this.isActivat) {
            this.$refs.scTable.doLayout()
        }
    },
    deactivated() {
        this.isActivat = false
        // console.log("数据清空");
        this.$TOOL.data.remove()
    },
    methods: {
        //获取数据
        async getData() {
            this.loading = true
            // console.log('getdata')
            // console.log(this.queryform)
            this.$HTTP
                .post(this.url, { ...this.query, ...this.post_data, ...this.routeData, ...this.queryform })
                .then((res) => {
                    if (res.errcode != 0) {
                        this.emptyText = res.errmsg
                    } else {
                        this.emptyText = '暂无数据'
                        let tableData = (this.paging ? res.result.data : res.result) || []
                        this.userColumn = JSON.parse(JSON.stringify(this.columns))
                        this.dynamicColumns = this.columns.filter((el) => el.remote).map((el) => el.remote)
                        if (this.dynamicColumns.length) {
                            this.handleDynamicColumns(this.dynamicColumns, tableData)
                        }
                        if (this.paging) {
                            this.total = res.result.total || 0
                        }
                        this.tableData = tableData
                        console.log(this.userColumn);
                    }
                    this.$refs.scTable.setScrollTop(0)
                    this.$emit('dataChange', res, this.tableData)
                    if (res.errmsg == '选择的日期范围天数不是一周的倍数,请重新选择!') {
                        ElMessage.error(res.errmsg)
                    }
                })
                .finally(() => {
                    this.loading = false
                    this.modalLoading = false
                })
        },
        handleDynamicColumns(dynamicColumns, tableData) {
            dynamicColumns.forEach((column) => {
                let insertIndex = this.userColumn.findIndex((userColumn) => userColumn.prop === column.indexProp)
                tableData.forEach((userData, i) => {
                    userData[column.key] &&
                        userData[column.key].forEach((data) => {
                            let assignData = {}
                            for (let tmp of Object.entries(data)) {
                                assignData[data.id + tmp[0]] = tmp[1]
                            }
                            Object.assign(userData, assignData)
                            if (i < 1) {
                                if (column.insertMode === 'children') {
                                    if (this.userColumn[insertIndex].children) {
                                        let children = this.userColumn[insertIndex].children
                                        children.push({
                                            label: data[column.keysLable],
                                            prop: data.id + column.keysProp
                                        })
                                    } else {
                                        let children = []
                                        children.push({
                                            label: data[column.keysLable],
                                            prop: data.id + column.keysProp
                                        })
                                        this.userColumn[insertIndex].children = children
                                    }
                                } else {
                                    if (Array.isArray(column.keysProp)) {
                                        let children = []
                                        column.keysProp.forEach((tem) => {
                                            children.push(Object.assign({}, tem, { prop: data.id + tem.prop }))
                                        })
                                        this.userColumn.splice(insertIndex + 1, 0, {
                                            label: data[column.keysLable],
                                            children: children
                                        })
                                    } else {
                                        this.userColumn.splice(insertIndex, 0, {
                                            label: data[column.keysLable],
                                            prop: data.id + column.keysProp
                                        })
                                    }
                                }
                            }
                        })
                })
            })
        },
        finishEvent(e) {
            console.log('finishEvent')
            this.getData()
        },
        // 处理tab数据
        queryformdata(value) {
            this.queryform = value
            setTimeout(() => {
                this.getData()
            }, 100)
        },
        //分页点击
        paginationChange() {
            console.log(this.query.page)
            this.getData()
        },
        //条数变化
        pageSizeChange(size) {
            this.query.per_page = size
            this.getData()
        },
        //刷新数据
        async refresh() {
            Object.entries(this.queryform).forEach((el) => {
                if (!isNaN(el[1]) && el[1] instanceof Date) {
                    let dateConfig = this.formitems.find((obj) => obj.name === el[0])
                    if(dateConfig?.options?.valueFormat){
                        this.queryform[el[0]]=dayjs(el[1]).format(dateConfig?.options?.valueFormat)
                    }

                }
            })
            try {
                let resrule = await this.$refs.queryform.getruleFormRef()
                if (resrule) {
                    this.$refs.scTable.clearSelection()
                    this.getData()
                }
            } catch (error) {
                setTimeout(() => {
                    this.refresh()
                }, 100)
            }
        },
        //重置表单
        resetForm() {
            this.$refs.queryform.resetFields()
            // Object.assign(this.query,this.queryform)
            // this.queryform = {}
            this.getData()
            // console.log(formEl)
            // if (!formEl) return
            // formEl.resetFields()
        },
        //更新数据 合并上一次params
        upData(params, page = 1) {
            this.query.page = page
            this.$refs.scTable.clearSelection()
            Object.assign(this.tableParams, params || {})
            this.getData()
        },
        //重载数据 替换params
        reload(params, page = 1) {
            this.query.page = page
            this.tableParams = params || {}
            this.$refs.scTable.clearSelection()
            this.$refs.scTable.clearSort()
            this.$refs.scTable.clearFilter()
            this.getData()
        },
        //自定义变化事件
        columnSettingChange(userColumn) {
            this.userColumn = userColumn
            this.toggleIndex += 1
        },
        //自定义列保存
        async columnSettingSave(userColumn) {
            this.$refs.columnSetting.isSave = true
            try {
                await config.columnSettingSave(this.tableName, userColumn)
            } catch (error) {
                this.$message.error('保存失败')
                this.$refs.columnSetting.isSave = false
            }
            this.$message.success('保存成功')
            this.$refs.columnSetting.isSave = false
        },
        //自定义列重置
        async columnSettingBack() {
            this.$refs.columnSetting.isSave = true
            try {
                const column = await config.columnSettingReset(this.tableName, this.columns)
                this.userColumn = column
                this.$refs.columnSetting.usercolumn = JSON.parse(JSON.stringify(this.columns || []))
            } catch (error) {
                this.$message.error('重置失败')
                this.$refs.columnSetting.isSave = false
            }
            this.$refs.columnSetting.isSave = false
        },
        //排序事件
        sortChange(obj) {
            if (!this.remoteSort) {
                return false
            }
            if (obj.columns && obj.prop) {
                this.prop = obj.prop
                this.order = obj.order
            } else {
                this.prop = null
                this.order = null
            }
            this.getData()
        },
        //本地过滤
        filterHandler(value, row, column) {
            const property = column.property
            return row[property] === value
        },
        //过滤事件
        filterChange(filters) {
            if (!this.remoteFilter) {
                return false
            }
            Object.keys(filters).forEach((key) => {
                filters[key] = filters[key].join(',')
            })
            this.upData(filters)
        },
        configSizeChange() {
            this.$refs.scTable.doLayout()
        },
        //插入行 unshiftRow
        unshiftRow(row) {
            this.tableData.unshift(row)
        },
        //插入行 pushRow
        pushRow(row) {
            this.tableData.push(row)
        },
        //根据key覆盖数据
        updateKey(row, rowKey = this.rowKey) {
            this.tableData
                .filter((item) => item[rowKey] === row[rowKey])
                .forEach((item) => {
                    Object.assign(item, row)
                })
        },
        //根据index覆盖数据
        updateIndex(row, index) {
            Object.assign(this.tableData[index], row)
        },
        //根据index删除
        removeIndex(index) {
            this.tableData.splice(index, 1)
        },
        //根据index批量删除
        removeIndexes(indexes = []) {
            indexes.forEach((index) => {
                this.tableData.splice(index, 1)
            })
        },
        //根据key删除
        removeKey(key, rowKey = this.rowKey) {
            this.tableData.splice(
                this.tableData.findIndex((item) => item[rowKey] === key),
                1
            )
        },
        //根据keys批量删除
        removeKeys(keys = [], rowKey = this.rowKey) {
            keys.forEach((key) => {
                this.tableData.splice(
                    this.tableData.findIndex((item) => item[rowKey] === key),
                    1
                )
            })
        },
        //原生方法转发
        clearSelection() {
            this.$refs.scTable.clearSelection()
        },
        toggleRowSelection(row, selected) {
            this.$refs.scTable.toggleRowSelection(row, selected)
        },
        toggleAllSelection() {
            this.$refs.scTable.toggleAllSelection()
        },
        toggleRowExpansion(row, expanded) {
            this.$refs.scTable.toggleRowExpansion(row, expanded)
        },
        setCurrentRow(row) {
            this.$refs.scTable.setCurrentRow(row)
        },
        clearSort() {
            this.$refs.scTable.clearSort()
        },
        clearFilter(columnKey) {
            this.$refs.scTable.clearFilter(columnKey)
        },
        doLayout() {
            this.$refs.scTable.doLayout()
        },
        sort(prop, order) {
            this.$refs.scTable.sort(prop, order)
        }
    }
}
</script>

<style scoped>
.scTable {
}
.scTable-table {
    height: calc(100% - 50px);
}
.scTable-page {
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
}
.scTable-do {
    white-space: nowrap;
}
.scTable:deep(.el-table__footer) .cell {
    font-weight: bold;
}
.scTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-horizontal {
    height: 12px;
    border-radius: 12px;
}
.scTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-vertical {
    width: 12px;
    border-radius: 12px;
}
.button_lst {
    display: flex;
    justify-content: space-between;
}
.button_left {
    display: flex;
}
.button_left_div {
    margin-right: 15px;
}
</style>
