<template>
    <!-- input -->
    <template v-if="item.component == 'input'">
        <el-input
            v-model="form[item.name]"
            :disabled="item.options.disabled"
            :type="item.options.type"
            :placeholder="item.options.placeholder ? item.options.placeholder : '请输入'"
            clearable
            :maxlength="item.options.maxlength"
            show-word-limit
        ></el-input>
    </template>
    <!-- inputtap -->
    <template v-else-if="item.component == 'inputtap'">
        <yy_inputtap
            :form="form"
            :colums="item"
        ></yy_inputtap>
    </template>
    <!-- fillvalue -->
    <template v-else-if="item.component == 'fillvalue'">
        <el-input
            v-model="item.options[form[item.name]]"
            :disabled="item.disabled"
            :type="item.options.type"
            :placeholder="item.options.placeholder"
            clearable
            :maxlength="item.options.maxlength"
            show-word-limit
        ></el-input>
    </template>
    <!-- array -->
    <template v-else-if="item.component == 'array'">
        <el-col
            v-for="array in item.optionArray"
            :key="array"
        >
            <span
                v-for="inde in JSON.parse(form[item.name])"
                :key="inde.id"
            >
                <el-input
                    v-model="inde[array.name]"
                    :disabled="item.options.disabled"
                    :type="item.options.type"
                    :placeholder="item.options.placeholder"
                    clearable
                    :maxlength="item.options.maxlength"
                    show-word-limit
                ></el-input>
            </span>
        </el-col>
    </template>
    <!-- arraycard -->
    <template v-else-if="item.component == 'arraycard'">
        <el-col
            v-for="(itm, ind) in form[item.name]"
            :key="ind"
            :span="8"
        >
            <el-card
                class="box-card"
                shadow="never"
            >
                <div
                    v-for="(im, id) in item.options.items"
                    :key="id"
                    class="box-card-item"
                >
                    <div class="label">{{ im.label }}</div>
                    <template v-if="im.component == 'select'">
                        <template
                            v-for="(imitems, iditems) in im.options.items"
                            :key="iditems"
                        >
                            <template v-if="imitems.value == JSON.parse(itm)[im.name]">
                                <el-tag
                                    class="value"
                                    :type="imitems.type"
                                >
                                    {{ imitems.label }}
                                </el-tag>
                            </template>
                        </template>
                    </template>
                    <template v-else>
                        <div class="value">{{ JSON.parse(itm)[im.name] }}</div>
                    </template>
                </div>
            </el-card>
        </el-col>
    </template>
    <!-- userlist -->
    <template v-else-if="item.component == 'userlist'">
        <div class="userlist">
            <span
                v-for="array in item.optionUser"
                :key="array"
            >
                <span
                    v-for="inde in JSON.parse(form[item.name])"
                    :key="inde.id"
                >
                    <span
                        v-for="obj in inde"
                        :key="obj.id"
                        style="color: #a8abb2"
                    >
                        {{ obj[array.name] + ' ' }}
                    </span>
                </span>
            </span>
        </div>
    </template>
    <!-- checkboxlimits  -->
    <template v-else-if="item.component == 'checkboxlimits'">
        <yy_checkbox
            v-model="form[item.name]"
            :model="item"
            :form="form"
            ref="yycheckbox"
        >
        </yy_checkbox>
    </template>
    <!-- tree -->
    <template v-else-if="item.component == 'treecheck'">
        <yy_tree
            v-model="form[item.name]"
            :model="item"
            :defaultProps = item.options.defaultProps
            :form="form"
        ></yy_tree>
    </template>
    <!-- powerTreeCheck -->
    <template v-else-if="item.component == 'powerTreeCheck'">
        <yy_checkboxDialog
            :model="form[item.name]"
            :form="form"
            :names="item.name"
            :label="item.options.butlabel"
            :remote="item.options.remote"
            :childrenProp="item.options.childrenProp"
            :name="item.options.name"
        ></yy_checkboxDialog>
    </template>
    <!-- checkbox -->
    <template v-else-if="item.component == 'checkbox'">
        <template v-if="item.name">
            <el-checkbox
                v-model="form[item.name][_item.name]"
                :label="_item.label"
                v-for="(_item, _index) in item.options.items"
                :key="_index"
            ></el-checkbox>
        </template>
        <template v-else>
            <el-checkbox
                v-model="form[_item.name]"
                :label="_item.label"
                v-for="(_item, _index) in item.options.items"
                :key="_index"
            ></el-checkbox>
        </template>
    </template>
    <!-- checkboxGroup -->
    <template v-else-if="item.component == 'checkboxGroup'">
        <el-checkbox-group v-model="form[item.name]">
            <el-checkbox
                v-for="_item in item.options.items"
                :key="_item.value"
                :label="_item.value"
                >{{ _item.label }}</el-checkbox
            >
        </el-checkbox-group>
    </template>
    <!-- yyTableSelect -->
    <template v-else-if="item.component == 'scTableSelect'">
        <yy_table_select
            v-model="form[item.name]"
            :apiObj="item.options.api"
            :postData="item.options.postData"
            :params="params"
            :table-width="600"
            :props="item.options.props"
            :querycolum="item.options.querycolum"
            @change="change"
        >
            <template
                v-for="(itm, inx) in item.options.tablecolum"
                :key="inx"
            >
                <el-table-column
                    :prop="itm.prop"
                    :label="itm.label"
                ></el-table-column>
            </template>
        </yy_table_select>
    </template>
    <!-- upload -->
    <template v-else-if="item.component == 'upload'">
        <el-upload
            class="avatar-uploader"
            :action="item.url"
            :show-file-list="false"
            :before-upload="beforePicUpload"
            :http-request="request"
            :on-success="success"
            :on-error="error"
        >
            <img
                v-if="imageUrl"
                :src="imageUrl"
                class="avatar"
            />
            <el-icon
                v-else
                class="avatar-uploader-icon"
            >
                <el-icon-plus></el-icon-plus>
            </el-icon>
        </el-upload>
    </template>
    <!-- uploadVideo -->
    <template v-else-if="item.component == 'uploadVideo'">
        <el-upload
            class="avatar-uploader"
            :action="item.url"
            :show-file-list="false"
            :before-upload="beforeVideoUpload"
            :http-request="request"
            :on-success="success"
            :on-error="error"
        >
            <video
                v-if="imageUrl"
                class="avatar"
                :src="imageUrl"
                :autoplay="true"
                controls="controls"
                muted
                loop
            ></video>
            <el-icon
                v-else
                class="avatar-uploader-icon"
            >
                <el-icon-plus></el-icon-plus>
            </el-icon>
        </el-upload>
    </template>
    <!-- uploadMusic -->
    <template v-else-if="item.component == 'uploadMusic'">
        <el-upload
            :action="item.options.url"
            :show-file-list="false"
            :before-upload="beforeMusicUpload"
            :http-request="request"
            :on-success="musicSuccess"
        >
            <el-button>{{ item.options.text }}</el-button>
            <template v-if="this.form[this.item.name]">
                <audio
                    :src="this.form[this.item.name]"
                    controls
                    style="margin-left: 10px; width: 400px; height: 40px"
                ></audio>
            </template>
            <template #tip>
                <div class="el-upload__tip">{{ item.options.message }}</div>
            </template>
        </el-upload>
    </template>
    <!-- multipleSelectionList -->
    <template v-else-if="item.component == 'multipleSelectionList'">
        <el-row>
            <el-col v-if="!item.options.disabled">
                <el-button @click="dialogVisibleMedia = true">{{
                    item.options.title ? item.options.title : '选择素材'
                }}</el-button>
                <el-dialog
                    :title="item.options.title ? item.options.title : '选择素材'"
                    width="60%"
                    v-model="dialogVisibleMedia"
                    :before-close="handleClose"
                >
                    <yp_list_tree
                        :url="item.options.url"
                        :globalHeight="item.options.globalHeight"
                        :paging="item.options.paging"
                        :isSingleoption="item.options.isSingleoption"
                        :postData="item.options.postdata"
                        :columns="item.options.columns"
                        :sideNav="item.options.sideNav"
                        :queryItem="item.options.queryitem"
                        :buttonList="item.options.buttonList"
                        @mediaList="mediaList"
                    ></yp_list_tree>
                </el-dialog>
            </el-col>
            <el-col>
                <div
                    v-if="this.selectedData.length >= 1"
                    class="uploadMediaList-list"
                >
                    <template
                        v-for="(tag, ix) in this.selectedData"
                        :key="ix"
                    >
                        <el-tag
                            closable
                            @close="handleRemove(ix)"
                        >
                            {{ tag[item.options.name] }}
                        </el-tag>
                    </template>
                </div>
            </el-col>
        </el-row>
    </template>
    <!-- radio -->
    <template v-else-if="item.component == 'radiobox'">
        <yy_radiobox
            :form="form"
            :name="item.name"
            :remote="item.options.remote"
            :columns="item.options.columns"
            :radio="item.options.radio"
            :sideNav="item.options.sideNav"
            :queryItem="item.options.queryitem"
            :buttonList="item.options.buttonList"
            :cardColums="item.options.cardColums"
        ></yy_radiobox>
    </template>
    <!-- switch -->
    <template v-else-if="item.component == 'switch'">
        <template v-if="item.options">
            <el-switch
                v-model="form[item.name]"
                :active-value="item.options.activevalue"
                :inactive-value="item.options.inactivevalue"
                :disabled="item.options.disabled"
            />
        </template>
        <template v-else>
            <el-switch v-model="form[item.name]" />
        </template>
    </template>
    <!-- select -->
    <template v-else-if="item.component == 'select'">
        <yy_select
            ref="yyselect"
            v-model="form[item.name]"
            :model="item.name"
            :form="form"
            @selectlist="selectlist"
            @changeremoteitems="changeremoteitems"
            :remote="item.options.remote"
            :items="item.options.items"
            :remoteitems="item.options.remoteitems"
            :multiple="item.options.multiple"
            :getselectremote="getselectremote"
            :disabled="item.options.disabled"
            :name="item.name"
        >
        </yy_select>
    </template>
    <!-- selectUser -->
    <template v-else-if="item.component == 'selectUser'">
        <yy_selectuser
            v-model="form[item.name]"
            :items="item.options"
            :model="form[item.name]"
        >
        </yy_selectuser>
    </template>
    <!-- cascader -->
    <template v-else-if="item.component == 'cascader'">
        <el-cascader
            v-model="form[item.name]"
            :options="item.options.items"
            clearable
        ></el-cascader>
    </template>
    <!-- date -->
    <template v-else-if="item.component == 'date'">
        <el-date-picker
            v-model="form[item.name]"
            :type="item.options.type"
            :range-separator="item.options.rangeseparator"
            :start-placeholder="item.options.startplaceholder"
            :end-placeholder="item.options.endplaceholder"
            :shortcuts="item.options.shortcuts"
            :default-time="item.options.defaultTime"
            :value-format="item.options.valueFormat"
            :placeholder="item.options.placeholder || '请选择'"
            :disabled="item.options.disabled"
        ></el-date-picker>
    </template>
    <!-- time -->
    <template v-else-if="item.component == 'time'">
        <el-time-select
            v-model="form[item.name]"
            arrow-control
            :picker-options="item.options.pickeroptions"
            :placeholder="item.options.placeholder || '选择时间'"
        >
        </el-time-select>
    </template>
    <!-- timepicker -->
    <template v-else-if="item.component == 'timepicker'">
        <el-time-picker
            v-model="form[item.name]"
            :value-format="item.options.valueFormat"
            :picker-options="item.options.pickeroptions"
            :placeholder="item.options.placeholder || '选择时间'"
            :disabled="item.options.disabled"
        >
        </el-time-picker>
    </template>
    <!-- number -->
    <template v-else-if="item.component == 'number'">
        <el-input-number
            v-model="form[item.name]"
            :min="item.options.min"
            :max="item.options.max"
            controls-position="right"
            :disabled="item.options.disabled"
        ></el-input-number>
    </template>
    <!-- radio -->
    <template v-else-if="item.component == 'radio'">
        <el-radio-group v-model="form[item.name]">
            <el-radio
                v-for="_item in item.options.items"
                :key="_item.value"
                :label="_item.value"
                >{{ _item.label }}</el-radio
            >
        </el-radio-group>
    </template>
    <!-- color -->
    <template v-else-if="item.component == 'color'">
        <el-color-picker v-model="form[item.name]" />
    </template>
    <!-- rate -->
    <template v-else-if="item.component == 'rate'">
        <el-rate
            style="margin-top: 6px"
            v-model="form[item.name]"
        ></el-rate>
    </template>
    <!-- slider -->
    <template v-else-if="item.component == 'slider'">
        <el-slider
            v-model="form[item.name]"
            :marks="item.options.marks"
        ></el-slider>
    </template>
    <!-- tableselect -->
    <template v-else-if="item.component == 'tableselect'">
        <tableselect-render
            v-model="form[item.name]"
            :item="item"
        ></tableselect-render>
    </template>
    <!-- editor -->
    <template v-else-if="item.component == 'editor'">
        <sc-editor
            v-model="form[item.name]"
            placeholder="请输入"
            :height="400"
        ></sc-editor>
    </template>
    <!-- tag -->
    <template v-else-if="item.component == 'tag'">
        <template
            v-for="(_item, _index) in item.options.items"
            :key="_index"
        >
            <el-tag
                v-if="form[item.name] == _item.value"
                :type="_item.type"
                >{{ _item.label }}</el-tag
            >
        </template>
    </template>
    <!-- address -->
    <template v-else-if="item.component == 'address'">
        <el-input
            v-model="form[item.name]"
            disabled
        ></el-input>
        <el-button
            style="margin-top: 10px"
            @click="dialogVisible = true"
            >选择地址</el-button
        >
        <el-dialog
            title="选择地址"
            width="30%"
            v-model="dialogVisible"
            :before-close="handleClose"
        >
            <yy_address
                :item="item"
                :form="form"
                @downdialog="downdialog"
            ></yy_address>
        </el-dialog>
    </template>
    <!-- noComponent -->
    <template v-else>
        <el-tag type="danger">[{{ item.component }}] Component not found</el-tag>
    </template>
</template>

<script>
import { ElMessage } from 'element-plus'
import yy_tree from '@/components/yysoft/yy/yy_tree'
import yy_selectitems from '@/components/yysoft/yy/yy_selectitems'

export default {
    props: {
        item: { type: Object, default: () => {} },
        form: { type: Object, default: () => {} }
        // getselectremote: { type: Boolean, default: false }
    },
    components: {
        yy_tree,
        yy_selectitems
    },
    // emits: ['getremotedata'],
    data() {
        return {
            // isSave: false
            imageUrl: '',
            dialogVisible: false,
            dialogVisibleMedia: false,
            formData: {},
            // 视频
            //存放分页选中条目,回显用
            selectedData: []
        }
    },
    watch: {
        selectedData(val) {
            // console.log('168461651')
            // console.log(val)
        }
    },
    created() {},
    mounted() {
        // console.log('---------------------')
        // console.log(this.item)
        if (this.item.component == 'upload') {
            if (this.form[this.item.name]) {
                var imgtem = JSON.parse(this.form[this.item.name])
                this.imageUrl = imgtem[0]
            }
        } else if (this.item.component == 'multipleSelectionList') {
            if (this.form[this.item.name]) {
                var handleData = []
                console.log(this.form[this.item.name])
                this.form[this.item.name].forEach((el) => {
                    // console.log('---------------')
                    if (typeof el == 'string') {
                        handleData.push(JSON.parse(el))
                        this.selectedData = handleData
                    } else {
                        this.selectedData = this.form[this.item.name]
                    }
                    // console.log(this.selectedData)
                })
            }
        }
    },
    methods: {
        //处理下拉数据格式
        selectlist(postlock, name, formtype) {
            if (typeof postlock === 'object' && !Array.isArray(postlock)) {
                if (formtype) {
                    for (const key in postlock) {
                        this.form[key] = postlock[key]
                    }
                }
            } else if (Array.isArray(postlock)) {
                if (formtype) {
                    this.form[name] = JSON.stringify(postlock)
                }
            }
        },
        // 处理上传图片
        request(param) {
            // console.log(param)
            // console.log(this.item)
            Object.assign(param.data, this.formData)
            const data = new FormData()
            data.append(param.filename, param.file)
            for (const key in param.data) {
                data.append(key, param.data[key])
            }
            this.$HTTP
                .post(this.item.options.url, data, {
                    onUploadProgress: (e) => {
                        const complete = parseInt(((e.loaded / e.total) * 100) | 0, 10)
                        param.onProgress({ percent: complete })
                    }
                })
                .then((res) => {
                    param.onSuccess(res)
                    ElMessage({
                        message: '上传成功',
                        type: 'success'
                    })
                    // 添加apk本地存储
                    localStorage.setItem('apk', res.result)
                    // this.dialog = false
                })
                .catch((err) => {
                    param.onError(err)
                })
        },
        success(res, file) {
            this.imageUrl = res.result
            if (this.item.options.isAlone) {
                this.form[this.item.name] = res.result
            } else {
                var arrimg = []
                arrimg.push(res.result)
                this.form[this.item.name] = JSON.stringify(arrimg)
            }
        },
        musicSuccess(res, file) {
            // console.log('----------jhw-------414------')
            // console.log(res.result)
            this.form[this.item.name] = res.result
        },
        error(err) {
            ElMessage({
                message: '上传失败',
                type: err
            })
        },
        // 处理address遮罩层
        downdialog() {
            this.dialogVisible = false
        },
        // 多媒体列表遮罩层
        mediaList(data) {
            this.dialogVisibleMedia = false
            this.selectedData.push(...data)
            this.form[this.item.name] = this.selectedData
        },
        handleRemove(ix) {
            this.selectedData.splice(ix, 1)
            this.form[this.item.name] = this.selectedData
        },
        // 图片校验
        beforePicUpload(file) {
            if (!['image/jpeg', 'image/png', 'image/gif', 'image/webp'].includes(file.type)) {
                // this.$message.warning(`选择的文件类型 ${file.type} 非图像类文件`)
                this.$message.warning(`请上传图片格式文件`)
                return false
            }
            const maxSize = file.size / 1024 / 1024 < 10
            if (!maxSize) {
                this.$message.warning(`上传文件大小不能超过 10MB!`)
                return false
            }
        },
        // 视频校验
        beforeVideoUpload(file) {
            if (!['video/mp4', 'video/avi'].includes(file.type)) {
                // this.$message.warning(`选择的文件类型 ${file.type} 非视频类文件`)
                this.$message.warning(`请上传视频格式文件`)
                return false
            }
        },
        // 音频校验
        beforeMusicUpload(file) {
            if (!['audio/mpeg'].includes(file.type)) {
                // this.$message.warning(`选择的文件类型 ${file.type} 非音频类文件`)
                this.$message.warning(`请上传音频格式文件`)
                return false
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.box-card {
    width: 400px;
    .box-card-item {
        display: flex;
        .label {
            width: 120px;
        }
    }
}
.avatar-uploader {
    position: relative;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}
.avatar-uploader:hover {
    border-color: #409eff;
}
.avatar-uploader:hover .el-icon-circle-close {
    visibility: visible;
}
.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 150px;
    height: 150px;
    text-align: center;
}
.avatar {
    width: 150px;
    height: 150px;
    display: block;
}

// 视频
.uploadMediaList-list {
    display: flex;
    flex-wrap: wrap;
    margin-top: 20px;

    .uploadMediaList-list-item {
        position: relative;
        width: 200px;
        height: 200px;
        margin-right: 20px;
        margin-top: 20px;
        background-color: #fff;

        video {
            width: 100%;
            height: 100%;
        }
        img {
            width: 100%;
            height: 100%;
        }

        .uploadMediaList-item-actions {
            position: absolute;
            visibility: hidden;
            top: 10px;
            right: 10px;
            color: #e50027;
            font-size: 15px;
        }
    }

    .uploadMediaList-list-item:hover .uploadMediaList-item-actions {
        visibility: visible;
    }
}
</style>
