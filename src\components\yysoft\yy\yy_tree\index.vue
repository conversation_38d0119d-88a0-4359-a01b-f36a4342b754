<template>
    <div class="deliverySetting">
        <!-- <el-checkbox
            v-model="allCheck"
            class="allCheck"
            >全选</el-checkbox
        > -->
        <el-Tree
            id="userMtree"
            ref="tree"
            class="user-manage-tree"
            :data="treeData"
            :props="defaultProps"
            options="{options}"
            show-checkbox
            accordion
            :nodeKey="this.model.options.data"
            :default-expanded-keys="[1]"
            @check-change="checkChange"
            :expand-on-click-node="false"
            :default-expand-all="false"
            @node-click="handleOrganizationNodeClick"
            :check-strictly="isCheck"
        />
    </div>
</template>
<script>
export default {
    name: 'yy_tree',
    components: {},
    props: {
        model: { type: Object, default: () => {} },
        name: { type: String, default: '' },
        form: {
            type: Object,
            default: () => {
                return {}
            }
        }
        // defaultProps: {
        //     type: Object,
        //     default: {
        //         children: 'children',
        //         label: 'title'
        //     }
        // },
    },
    data() {
        return {
            treeData: [
                {
                    id: 62000020130505300,
                    title: '半',
                    className: 'jtipnwymn',
                    acts: [
                        { act: 'del', title: '删除' },
                        { act: 'modify', title: '编辑' },
                        { act: 'get_soft_all', title: '所属软件' },
                        { act: 'get_type_all', title: '全部类型' },
                        { act: 'get_version_all', title: '全部版本' }
                    ],
                    desc: 'Duis laboris ea eu',
                    lock: 0,
                    productId: 0,
                    type: 0,
                    createdAt: '1993-02-12 21:27:39',
                    updatedAt: '1982-03-12 05:10:33',
                    serviceType: 1
                },
                {
                    id: 520000198712097400,
                    title: '铁',
                    className: 'eiguvrsl',
                    acts: [
                        { act: 'get_ls', title: '产品展示' },
                        { act: 'get_info', title: '产品详情' },
                        { act: 'add', title: '查看' },
                        { act: 'del', title: '删除' },
                        { act: 'modify', title: '编辑' },
                        { act: 'get_soft_all', title: '所属软件' },
                        { act: 'get_type_all', title: '全部类型' },
                        { act: 'get_version_all', title: '全部版本' }
                    ],
                    desc: 'ex',
                    lock: 0,
                    productId: 0,
                    type: 0,
                    createdAt: '1995-06-22 02:12:25',
                    updatedAt: '2006-09-26 23:00:05',
                    serviceType: 1
                }
            ],
            checkedKeys: [],
            defaultProps: {
                children: 'acts',
                label: 'title'
            },
            isCheck: false,
            dataArr: [],
            datalist: {},
            allCheck: false
        }
    },
    computed: {},
    created() {},
    mounted() {
        this.getData()
    },
    watch: {},
    methods: {
        // 深拷贝
        deepClone(obj) {
            //判断拷贝的要进行深拷贝的是数组还是对象，是数组的话进行数组拷贝，对象的话进行对象拷贝
            var objClone = Array.isArray(obj) ? [] : {}
            //进行深拷贝的不能为空，并且是对象或者是
            if (obj && typeof obj === 'object') {
                for (let key in obj) {
                    if (obj.hasOwnProperty(key)) {
                        if (obj[key] && typeof obj[key] === 'object') {
                            objClone[key] = this.deepClone(obj[key])
                        } else {
                            objClone[key] = obj[key]
                        }
                    }
                }
            }
            return objClone
        },
        //处理远程选项数据
        getData() {
            this.$HTTP.get(this.model.options.remote.api, this.model.options.remote.postData).then((res) => {
                if (res.errcode != 0) {
                    ElMessage.error(res.errmsg)
                } else {
                    if (this.model.type == 'add') {
                        this.treeData = res.result
                    } else if (this.model.type == 'info' || 'edit') {
                        let arrAy = JSON.parse(this.form[this.model.name])
                        arrAy.forEach((arr) => {
                            this.checkedKeys.push(arr[this.model.options.data])
                        })
                        this.isCheck = true //重点：回显之前一定要设置为true
                        this.$nextTick(() => {
                            this.$refs.tree.setCheckedKeys(this.checkedKeys) //给树节点赋值回显
                            this.isCheck = false //重点： 赋值完成后 设置为false
                        })
                        this.treeData = res.result
                    }
                }
            })
        },
        /**、点击树节点触发事件 */
        handleOrganizationNodeClick(obj) {
            this.currentOrganization = obj

            if (!obj.children) {
                obj.children = []
            }
            if (obj.children.length === 0 && obj.HasChild) {
                // f发起请求
                let resultRegionInfo = this.asyncTreeData(obj.Id)
                resultRegionInfo.then((data) => {
                    obj.children = data.children
                    this.organizationTableData = obj.children
                })
            }
        },
        //节点被选中和取消时触发
        checkChange(data, checked) {
            ;(this.checkedKeys = []), (this.dataArr = []), (this.datalist = {})
            // 获取选中的数据
            // 父节点
            this.$refs.tree.getHalfCheckedNodes().forEach((res) => {
                console.log(res)
                this.datalist = {
                    [this.model.options.data]: res[this.model.options.data],
                    [this.model.options.name]: res[this.model.options.name],
                    [this.model.options.fudata]: res[this.model.options.fudata]
                }
                this.dataArr.push(this.datalist)
            })
            // 子节点
            this.$refs.tree.getCheckedNodes().forEach((res) => {
                this.datalist = {
                    [this.model.options.data]: res[this.model.options.data],
                    [this.model.options.name]: res[this.model.options.name],
                    [this.model.options.fudata]: res[this.model.options.fudata]
                }
                this.dataArr.push(this.datalist)
            })
            this.form[this.model.name] = JSON.stringify(this.dataArr)
            console.log(this.form)
        }
    }
}
</script>
<style lang="scss">
.deliverySetting {
    width: 100%;
    min-width: 500px;
    padding: 0;
    border: 1px solid #e3e5e8;
    border-radius: 8px;

    .allCheck {
        margin-left: 24px;
    }
}
</style>
