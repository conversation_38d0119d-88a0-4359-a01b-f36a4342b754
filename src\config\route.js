/*
 * @author: 吉慧雯
 * @name: 云一门岗路由配置
 * @desc: 介绍
 * @LastEditTime: 2023-02-01
 * @FilePath: /eyc3_car_pc/src/config/route.js
 */
// 静态路由配置
// 书写格式与动态路由格式一致，全部经由框架统一转换
// 比较动态路由在meta中多加入了role角色权限，为数组类型。一个菜单是否有权限显示，取决于它以及后代菜单是否有权限。
// routes 显示在左侧菜单中的路由(显示顺序在动态路由之前)

const routes = [
    {
        name: 'index',
        path: '/index',
        component: 'index/index',
        meta: {
            icon: 'el-icon-House',
            title: '首页'
        },
        children: []
    },
    {
        path: '/index/list',
        name: 'indexList',
        component: 'index/list',
        meta: {
            title: '任务列表',
            hidden: true
        }
    },
    // 常用配置
    {
        name: 'common',
        path: '/common',
        meta: {
            icon: 'el-icon-Files',
            title: '常用配置'
        },
        children: [
            {
                path: '/common/task_assign',
                name: 'task_assign',
                component: 'common/task_assign/list',
                meta: {
                    icon: 'el-icon-DocumentChecked',
                    title: '任务分配',
                    primission: 1
                },
                children: [
                    {
                        path: '/common/task_assign/add',
                        name: 'task_assign_add',
                        component: 'common/task_assign/crud/add',
                        meta: {
                            hidden: true,
                            title: '新增任务'
                        }
                    },
                    {
                        path: '/common/task_assign/edit',
                        name: 'task_assign_edit',
                        component: 'common/task_assign/crud/edit',
                        meta: {
                            hidden: true,
                            title: '修改任务'
                        }
                    }
                ]
            },
        ]
    },
    // 基础配置
    {
        name: 'Basic',
        path: '/basic',
        meta: {
            icon: 'el-icon-folder-opened',
            title: '基础配置'
        },
        children: [
            {
                path: '/basic/task_group',
                name: 'task_group',
                component: 'basic/task_group/list',
                meta: {
                    icon: 'el-icon-Discount',
                    title: '任务分组',
                    primission: 2
                },
                children: [
                    {
                        path: '/basic/task_group/add',
                        name: 'task_group_add',
                        component: 'basic/task_group/crud/add',
                        meta: {
                            hidden: true,
                            title: '新增任务分组'
                        }
                    },
                    {
                        path: '/basic/task_group/edit',
                        name: 'task_group_edit',
                        component: 'basic/task_group/crud/edit',
                        meta: {
                            hidden: true,
                            title: '修改任务分组'
                        }
                    }
                ]
            },
            {
                path: '/common/managementPost',
                name: 'managementPost',
                component: 'common/managementPost/list',
                meta: {
                    icon: 'el-icon-DocumentChecked',
                    title: '岗位管理',
                    primission: 1
                },
                children: [
                    {
                        path: '/common/managementPost/add',
                        name: 'managementPostAdd',
                        component: 'common/managementPost/crud/add',
                        meta: {
                            hidden: true,
                            title: '新增岗位管理'
                        }
                    },
                    {
                        path: '/common/managementPost/edit',
                        name: 'managementPostEdit',
                        component: 'common/managementPost/crud/edit',
                        meta: {
                            hidden: true,
                            title: '编辑岗位管理'
                        }
                    }
                ]
            },
            {
                path: '/basic/task_flow',
                name: 'task_flow',
                component: 'basic/task_flow/list',
                meta: {
                    icon: 'el-icon-Connection',
                    title: '任务流',
                    primission: 3
                },
                children: [
                    {
                        path: '/basic/task_flow/add',
                        name: 'task_flow_add',
                        component: 'basic/task_flow/crud/add',
                        meta: {
                            hidden: true,
                            title: '新增任务流'
                        }
                    },
                    {
                        path: '/basic/task_flow/edit',
                        name: 'task_flow_edit',
                        component: 'basic/task_flow/crud/edit',
                        meta: {
                            hidden: true,
                            title: '修改任务流'
                        }
                    }
                ]
            },
            // 权限管理
            {
                path: '/basic/power',
                name: 'BasicPower',
                component: 'basic/power/list',
                meta: {
                    icon: 'el-icon-Key',
                    title: '权限管理',
                    primission: 6
                },
                children: [
                    {
                        path: '/basic/power/add',
                        name: 'BasicPowerAdd',
                        component: 'basic/power/crud/add',
                        meta: {
                            hidden: true,
                            title: '新增权限管理'
                        }
                    },
                    {
                        path: '/basic/power/edit',
                        name: 'BasicPowerEdit',
                        component: 'basic/power/crud/edit',
                        meta: {
                            hidden: true,
                            title: '编辑权限管理'
                        }
                    }
                ]
            }
        ]
    },
    // 审计日志
    // {
    //     name: 'audit_log',
    //     path: '/audit_log',
    //     component: 'audit_log',
    //     meta: {
    //         icon: 'el-icon-Edit',
    //         title: '审计日志',
    //         primission: 6
    //     },
    //     children: []
    // },
    // 关于
    // {
    //     name: 'about',
    //     path: '/about',
    //     component: 'about',
    //     meta: {
    //         icon: 'el-icon-info-filled',
    //         title: '关于',
    //         primission: 6
    //     },
    //     children: []
    // },
    // 系统配置
    {
        name: 'system',
        path: '/system',
        component: 'system',
        meta: {
            icon: 'el-icon-setting',
            title: '系统配置',
            primission: 6
        },
        children: []
    }
]

export default routes
