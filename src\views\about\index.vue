<template>
    <el-main>
        <el-row :gutter="15">
            <el-col :lg="24">
                <el-card
                    shadow="never"
                    class="aboutTop"
                >
                    <div class="aboutTop-info">
                        <img src="/img/logo.png" />
                        <h2>{{ fileApi }}</h2>
                        <p>{{ fileApi1 }}</p>
                    </div>
                </el-card>
                <el-card shadow="never">
                    <el-descriptions
                        border
                        :column="3"
                    >
                        <el-descriptions-item label="授权设备">{{ setData }}台</el-descriptions-item>
                    </el-descriptions>
                </el-card>
            </el-col>
        </el-row>
    </el-main>
</template>

<script>
// import packageJson from '../../../package.json'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
export default {
    name: 'about',
    data() {
        return {
            // data: packageJson,
            setData: '',
            fileApi: import.meta.env.VITE_APP_TITLE,
            fileApi1: import.meta.env.VITE_APP_VERSION
        }
    },
    mounted() {
        this.handler()
    },
    methods: {
        // 获取授权设备数量
        handler() {
            this.$HTTP
                .get('sys/get_authorized_device')
                .then((res) => {
                    console.log(res, 'res')
                    if (res.errcode == 0) {
                        this.setData = res.result
                    } else {
                        ElMessage.error(res.errmsg)
                    }
                })
                .finally(() => {})
        }
    }
}
</script>

<style scoped>
.aboutTop {
    border: 0;
    background: linear-gradient(to right, #8e54e9, #4776e6);
    color: #fff;
}

.aboutTop-info {
    text-align: center;
}

.aboutTop-info img {
    width: 100px;
}

.aboutTop-info h2 {
    font-size: 26px;
    margin-top: 15px;
}

.aboutTop-info p {
    font-size: 16px;
    margin-top: 10px;
}
</style>
