<template>
    <template v-if="this.component == 'tag'">
        <template
            v-for="(item, index) in this.options.items"
            :key="index"
        >
            <template v-if="item.value == this.form">
                <el-tag :type="item.type ? item.type : ''">{{ item.label }}</el-tag>
            </template>
        </template>
    </template>
    <template v-else-if="this.component == 'objArr'">
        <template v-if="this.form">
            <template
                v-for="(item, index) in JSON.parse(this.form)[this.options.objName]"
                :key="index"
            >
                <span style="margin-right: 10px">{{ item[this.options.arrName] }}</span>
            </template>
        </template>
    </template>
    <template v-else-if="this.component == 'Array'">
        <template v-if="this.form">
            <template
                v-for="(item, index) in JSON.parse(this.form)"
                :key="index"
            >
                <span style="margin-right: 10px">{{ item[this.options.prop] }}</span>
            </template>
        </template>
    </template>
    <template v-else-if="this.component == 'Arraystring'">
        <template v-if="this.form">
            <template
                v-for="(item, index) in JSON.parse(this.form)"
                :key="index"
            >
                <span style="margin-right: 10px">{{ item }}</span>
            </template>
        </template>
    </template>
    <template v-else-if="this.component == 'color'">
        <div :style="{ background: form, width: '15px', height: '15px' }"></div>
    </template>
    <template v-else-if="this.component == 'numImage'">
        <template
            v-for="(item, index) in this.options.items"
            :key="index"
        >
            <template v-if="item.value == this.form">
                <el-tag>{{ item.label }}</el-tag>
                <div>
                    <el-image
                        style="width: 100px; height: 100px"
                        :src="item.img"
                        fit="contain"
                    ></el-image>
                </div>
            </template>
        </template>
    </template>
    <template v-else-if="this.component == 'image'">
        <template v-if="this.form">
            <el-image
                style="width: 80px; height: 80px; border-radius: 5px"
                :src="form + '?x-oss-process=image/resize,w_300/format,webp'"
                :zoom-rate="1.2"
                fit="cover"
            />
        </template>
        <template v-else>
            <div class="img-text">暂无图片</div>
        </template>
    </template>
    <template v-else-if="this.component == 'images'">
        <template v-if="this.form">
            <template v-if="JSON.parse(this.form).length >= 1">
                <template
                    v-for="(item, index) in JSON.parse(this.form)"
                    :key="index"
                >
                    <el-image
                        style="width: 80px; height: 80px; border-radius: 5px"
                        :src="item + '?x-oss-process=image/resize,w_300/format,webp'"
                        :zoom-rate="1.2"
                        fit="cover"
                    />
                </template>
            </template>
            <template v-else>
                <div class="img-text">暂无图片</div>
            </template>
        </template>
        <template v-else>
            <div class="img-text">暂无图片</div>
        </template>
    </template>
    <template v-else-if="this.component == 'link'">
        <el-link
            :href="form"
            target="_blank"
            >{{ form }}</el-link
        >
    </template>
    <template v-else-if="this.component == 'linkArr'">
        <template
            v-for="(item, index) in JSON.parse(JSON.stringify(form))"
            :key="index"
        >
            <el-link
                :href="item[this.options.href]"
                target="_blank"
                style="margin-right: 10px"
                >{{ item[this.options.text] }}</el-link
            >
        </template>
    </template>
    <template v-else-if="this.component == 'audio'">
        <template v-if="form">
            <audio
                :src="form"
                controls
                style="margin-left: 10px; width: 400px; height: 40px"
            ></audio>
        </template>
        <template v-else>暂无音频</template>
    </template>
    <template v-else-if="this.component == 'pickTime'">
        {{ form.split(' ')[1] }}
    </template>
    <template v-else-if="this.component == 'arrayObj'">
        <template v-if="form">
            <template
                v-for="(el, index) in JSON.parse(form)"
                :key="index"
            >
                <span style="padding-right: 5px">{{ el[item.options.key] }}</span>
            </template>
        </template>
    </template>
    <template v-else-if="this.component == 'userlst'">
        <template v-if="form">
            <template
                v-for="(el, index) in JSON.parse(form)"
                :key="index"
            >
                <template
                    v-for="(item, index) in el"
                    :key="index"
                >
                    <span style="padding-right: 5px">{{ item[this.options.key] }}</span>
                </template>
            </template>
        </template>
    </template>
    <template v-else-if="this.component == 'userlstStr'">
        <span
            v-for="(userlst, index) in userlstStr"
            :key="index"
        >
            {{ userlst.name + ' ' }}
        </span>
    </template>
    <!-- <slot :name="item.prop" v-bind="scope" v-if="item.component == 'userlstStr'">
        <span v-for="(userlst, index) in JSON.parse(scope.row[item.prop]).users" :key="index">
            {{ userlst.name + ' ' }}
        </span>
    </slot> -->
    <!-- <template v-else-if="this.component == 'medias'">
        {{ this.options.items[this.formAll[this.options.relateProp]] }}
        <template v-if="this.options.items[this.formAll[this.options.relateProp]].name == 'pic'">
            <el-image
                style="width: 100px; height: 100px"
                :src="form"
                fit="content"
            ></el-image>
        </template>
        <template v-else-if="this.options.items[this.formAll[this.options.relateProp]].name == 'video'">
            <video
                style="width: 100px; height: 100px"
                :src="form"
            ></video>
        </template>
        <template v-else-if="this.options.items[this.formAll[this.options.relateProp]].name == 'web'">
            <el-link
                :href="form"
                target="_blank"
                >{{ form }}</el-link
            >
        </template> -->

    <!-- <template v-if="this.mediaType == 'video'"></template>
        <template v-else-if="this.mediaType == 'pic'">
            <el-image
                style="width: 100px; height: 100px"
                :src="form"
                fit="content"
            ></el-image>
        </template>
        <template v-else-if="this.mediaType == 'web'"></template> -->
    <!-- </template> -->
    <template v-else>
        {{ form }}
    </template>
</template>


<script>
export default {
    name: '',
    props: {
        form: { type: String, default: '' },
        item: { type: Object, default: () => {} },
        formAll: { type: Object, default: () => {} },
        component: { type: String, default: '' },
        options: { type: Object, default: () => {} }
    },
    data() {
        return {
            objArrStr: '',
            mediaType: '',
            userlstStr: []
        }
    },
    created() {
        if (this.component == 'userlstStr') {
            console.log('111111111111111')
            if (this.form.length > 0) {
                this.userlstStr = JSON.parse(this.form).users
            }
        }
    },
    // 监听form的变化
    watch: {
        form(newVal, oldVal) {
            if (this.component == 'userlstStr') {
                if (this.form.length > 0) {
                    this.userlstStr = JSON.parse(this.form).users
                }
            }
        }
    },
    mounted() {},
    methods: {}
}
</script>

<style>
</style>