<!--
 * @author: 吉慧雯
 * @name: 计划任务
 * @desc: 介绍
 * @LastEditTime: 2022-12-30 15:55:07
 * @FilePath: \eyc3_guard_pc/src/views/work/basic/plan
-->
<template>
    <yp_list
        ref="table"
        :url="url"
        :columns="columns"
        :formitems="formitems"
        :buttonList="buttonList"
        @info-data="infodata"
    >
    </yp_list>
</template>

<script>
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
export default {
    name: 'BasicPlan',
    data() {
        return {
            url: 'group/get_ls',
            columns: [
                {
                    label: '分组名称',
                    prop: 'title'
                },
                {
                    label: '操作',
                    prop: 'action',
                    component: 'button',
                    options: [
                        {
                            label: '详情',
                            component: 'detail',
                            options: {
                                title: '分组详情',
                                remote: {
                                    api: 'group/get_info', // 获取详情接口
                                    data: {
                                        // 获取详情接口数据
                                        id: '$id'
                                    }
                                },
                                items: [
                                    {
                                        label: '分组名称',
                                        prop: 'title'
                                    }
                                ]
                            }
                        },
                        {
                            label: '修改',
                            component: 'form',
                            options: {
                                name: 'task_group_edit', // 跳转页面名
                                remote: {
                                    state: 'edit', // 状态,'add'|'edit'|'detail'
                                    label: '编辑任务分组', // 页头名
                                    api: 'group/get_info', // 获取详情接口
                                    edit: 'group/post_modify', // 修改详情接口
                                    data: {
                                        // 获取详情接口数据
                                        id: '$id'
                                    },
                                    backrouter: '/basic/task_group'
                                }
                            }
                        },
                        {
                            label: '删除',
                            type: 'danger',
                            component: 'confirm',
                            options: {
                                label: '确认删除',
                                message: '是否确认删除?',
                                remote: {
                                    api: `group/post_del`,
                                    data: {
                                        id: '$id'
                                    }
                                }
                            }
                        }
                    ]
                }
            ],
            buttonList: [
                {
                    label: '新建',
                    component: 'form',
                    options: {
                        name: 'task_group_add', // 跳转页面名
                        icon: 'el-icon-Plus', // 按钮图标
                        remote: {
                            state: 'add', // 状态
                            label: '新增任务分组', // 页头名
                            api: 'group/post_add', // 新增地址接口
                            data: {}
                        }
                    }
                }
            ],
            infodatas: {},
            resourceName: '资源不存在'
        }
    },
    //路由跳转进来 判断from是否有特殊标识做特殊处理
    // beforeRouteEnter(to, from, next) {
    //     next((vm) => {
    //         // console.log('-------------------------router')
    //         console.log(from.is)
    //         if (from.is) {
    //             //删除特殊标识，防止标签刷新重复执行
    //             delete from.is
    //             //执行特殊方法
    //             this.$refs.table.finishEvent()
    //         }
    //     })
    // },
    methods: {
        // 跳转详情接口
        goAdd() {
            // 跳转
            this.$router.push({
                name: 'task_group_add'
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.add-box {
    padding: 0 26px;
}
</style>
