<!--
 * @author: 风源
 * @name: 类名
 * @desc: 介绍
 * @LastEditTime: 2022-12-13 12:40:31
 * @FilePath: \eyc3_canyin_pc\src\views\report\visitor\list.vue
-->
<template>
	<yp_list
		ref="table"
		:url="url"
		row-key="id"
		:columns="columns"
		stripe
		:add="add"
		:derive="derive"
		:formitems="formitems"
	>
	</yp_list>
</template>

<script>
export default {
	name: "ReportVisitorList",
	data() {
		return {
			url: "report/get_visitor_bill_ls",
			columns: [
				{
					label: "姓名",
					prop: "user_name",
				},
				{
					label: "消费类型",
					prop: "desc",
				},

				{
					label: "就餐餐厅",
					prop: "dininghall_title",
				},
				{
					label: "订单类型",
					prop: "type",
					component: "fillvalue",
					options: [
						"固定金额订单",
						"自定义订单",
						"报餐订单",
						"点餐订单",
						"超市购物",
						"会议餐",
					],
				},
				{
					label: "支付类型",
					prop: "pay_type",
					component: "fillvalue",
					options: ["账户余额支付", "支付宝在线支付", "微信在线支付"],
				},
				{
					label: "订单状态",
					prop: "status",
					component: "fillvalue",
					options: [
						"已报餐/已点餐",
						"已支付",
						"已派送",
						"已就餐",
						"已评价",
						"已取消",
					],
				},
				{
					label: "消费金额",
					prop: "money",
				},
				{
					label: "补贴金额",
					prop: "subsidy_money",
				},

				{
					label: "虚拟账户余额",
					prop: "virtual_balance",
				},
				{
					label: "真实账户余额",
					prop: "real_balance",
				},
				{
					label: "菜品详情",
					prop: "dishess",
					component: "table",
					options: [
						{
							label: "名称",
							prop: "title"
						},
						{
							label: "分类",
							prop: "category_title"
						},
						{
							label: "数量",
							prop: "count"
						},
						{
							label: "价格",
							prop: "price"
						},
					],
				},
				{
					label: "餐时",
					prop: "repast_title",
					sorter: true,
				},
				{
					label: "窗口",
					prop: "window_title",
					sorter: true,
				},
				{
					label: "消费时间",
					prop: "updated_at",
					sorter: true,
				},
				{
					label: "操作",
					prop: "action",
					component: "button",
					options: [
						{
							label: "退款",
							type: "warning",
							component: "confirm",
							options: {
								label: "确认退款",
								message: "是否确认退款?",
								remote: {
									api: `visitor_bill/post_refund`,
									data: {
										id:"$id"
									},
								},
							},
						},
					],
				},			
			],
			derive: {
				filename:"访客账单",
			},
			formitems: [
				{
					label: "日期范围",
					name: "date_interval",
					value: [
						this.$TOOL.dateFormat(new Date(), "yyyy-MM-01"),
						this.$TOOL.dateFormat(new Date(), "yyyy-MM-dd"),
					],
					component: "date",
					options: {
						type: "daterange",
						rangeseparator: "至",
						startplaceholder: "开始日期",
						endplaceholder: "结束日期",
						valueFormat: "YYYY-MM-DD",
					},
					rules: [
						{
							required: true,
							message: "Please input Data",
							trigger: "change",
						},
					],
				},
				{
					label: "选择人员",
					name: "userlst",
					value: "",
					component: "selectUser"
				},
				{
					label: "餐厅",
					name: "dininghall_id",
					value: "",
					component: "select",
					options: {
						remote: {
							api: `dininghall/get_all`,
							data: { name: "b" },
							label: "title",
							value: "id",
						},
						items: [
							{
								label: "全部",
								value: "",
							},
						],
					},
				},
				{
					label: "餐时",
					name: "repast_id",
					value: "",
					component: "select",
					options: {
						remote: {
							api: `repast/get_all`,
							data: { dininghall_id: "$dininghall_id" },
							label: "title",
							value: "id",
						},
						items: [
							{
								label: "全部",
								value: "",
							},
						],
					},
				},
				{
					label: "窗口",
					name: "window_id",
					value: "",
					component: "select",
					options: {
						remote: {
							api: `window/get_all`,
							data: { dininghall_id: "$dininghall_id" },
							label: "title",
							value: "id",
						},
						items: [
							{
								label: "全部",
								value: "",
							},
						],
					},
				},				
			],
		};
	},
};
</script>

<style></style>
