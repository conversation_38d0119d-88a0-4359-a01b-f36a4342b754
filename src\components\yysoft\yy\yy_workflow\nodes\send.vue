<template>
    <div class="node-wrap">
        <div
            class="node-wrap-box"
            @click="show"
        >
            <div
                class="title"
                style="background: #3296fa"
            >
                <el-icon class="icon"><el-icon-UserFilled /></el-icon>
                <span>{{ nodeConfig.nodeName }}</span>
                <el-icon
                    class="close"
                    @click.stop="delNode()"
                    ><el-icon-close
                /></el-icon>
            </div>
            <div class="content">
                <span v-if="toText(nodeConfig)">{{ toText(nodeConfig) }}</span>
                <span
                    v-else
                    class="placeholder"
                    >请选择人员</span
                >
            </div>
        </div>
        <add-node v-model="nodeConfig.childNode"></add-node>
        <el-drawer
            title="抄送人设置"
            v-model="drawer"
            destroy-on-close
            append-to-body
            :size="500"
        >
            <template #header>
                <div class="node-wrap-drawer__title">
                    <label
                        @click="editTitle"
                        v-if="!isEditTitle"
                        >{{ form.nodeName }}<el-icon class="node-wrap-drawer__title-edit"><el-icon-edit /></el-icon
                    ></label>
                    <el-input
                        v-if="isEditTitle"
                        ref="nodeTitle"
                        v-model="form.nodeName"
                        clearable
                        @blur="saveTitle"
                        @keyup.enter="saveTitle"
                    ></el-input>
                </div>
            </template>
            <el-container>
                <el-main style="padding: 0 20px 20px 20px">
                    <el-form label-position="top">
                        <el-form-item label="人员安排">
                            <el-radio-group
                                v-model="form.uType"
                                :model="form.uType"
                            >
                                <el-radio :label="0">人员</el-radio>
                                <el-radio :label="1">岗位</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item
                            label="选择任务节点的人员"
                            v-if="form.uType == '0'"
                        >
                            <!-- <el-button
                                type="primary"
                                icon="el-icon-plus"
                                round
                                @click="selectHandle(1, form.nodeUserList)"
                                >选择人员</el-button
                            >
                            <div class="tags-list">
                                <el-tag
                                    v-for="(user, index) in form.nodeUserList"
                                    :key="user.id"
                                    closable
                                    @close="delUser(index)"
                                    >{{ user.name }}</el-tag
                                >
                            </div> -->
                            <yy_selectuser
                                v-model="form.userLst"
                                :model="form.userLst"
                                :isRecommend="isRecommend"
                                :items="{ type: 'edit' }"
                                :recommendOptions.sync="recommendOption"
                            ></yy_selectuser>
                        </el-form-item>
                        <el-form-item
                            v-if="form.uType == '1'"
                            label="岗位名称"
                            prop="unit_title"
                        >
                            <el-select
                                placeholder="请选择"
                                style="min-width: 420px"
                                v-model="form.post"
                                filterable
                                value-key="id"
                                @change="getUnit"
                            >
                                <el-option
                                    v-for="item in unitIds"
                                    :key="item.id"
                                    :label="item.title"
                                    :value="item"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item
                            v-if="form.uType == '1'"
                            label="岗位人数"
                            prop="unit_title"
                        >
                            <el-input-number
                                v-model="form.num"
                                :min="1"
                            ></el-input-number>
                        </el-form-item>
                    </el-form>
                </el-main>
                <el-footer>
                    <el-button
                        type="primary"
                        @click="save"
                        >保存</el-button
                    >
                    <el-button @click="drawer = false">取消</el-button>
                </el-footer>
            </el-container>
        </el-drawer>
    </div>
</template>

<script>
import addNode from './addNode'

export default {
    inject: ['select'],
    props: {
        modelValue: { type: Object, default: () => {} },
        isRecommend: { type: Boolean, default: false },
        recommendOptions: { type: Object, default: () => {} },
        postItems: { type: Array, default: () => [] }
    },
    components: {
        addNode
    },
    data() {
        return {
            nodeConfig: {},
            drawer: false,
            isEditTitle: false,
            form: {
                uType: 0,
                num: 1
            },
            recommendOption: {},
            unitIds: this.postItems
        }
    },
    watch: {
        modelValue() {
            this.nodeConfig = this.modelValue
        },
        recommendOptions(newVal, oldVal) {
            this.recommendOption = newVal
        }
    },
    mounted() {
        this.nodeConfig = this.modelValue
        this.recommendOption = this.recommendOptions
    },
    methods: {
        show() {
            this.form = {}
            this.form = JSON.parse(JSON.stringify(this.nodeConfig))
            console.log('测试回显', this.nodeConfig)
            if (this.isRecommend) {
                this.recommendOption.post.node_title = this.nodeConfig.nodeName
            }
            this.drawer = true
        },
        getUnit(item) {
            this.form.postionId = item.id
            this.form.postionName = item.title
        },
        editTitle() {
            this.isEditTitle = true
            this.$nextTick(() => {
                this.$refs.nodeTitle.focus()
            })
        },
        saveTitle() {
            this.isEditTitle = false
            if (this.isRecommend) {
                this.recommendOption.post.node_title = this.form.nodeName
            }
        },
        createStaticArray(num, id) {
            const validNum = Math.max(0, parseInt(num, 10) || 0)
            return Array(validNum).fill(id)
        },
        save() {
            // console.log('测试保存', this.form)
            if (this.form.uType == 0) {
                delete this.form.num
                delete this.form.post
                delete this.form.postionId
                delete this.form.postionName
                if (this.form.position) {
                    delete this.form.position
                }
            } else if (this.form.uType == 1) {
                delete this.form.userLst
                this.form.position = this.createStaticArray(this.form.num, this.form.postionId)
            }
            this.$emit('update:modelValue', this.form)
            this.drawer = false
        },
        delNode() {
            this.$emit('update:modelValue', this.nodeConfig.childNode)
        },
        delUser(index) {
            this.form.nodeUserList.splice(index, 1)
        },
        selectHandle(type, data) {
            this.select(type, data)
        },
        toText(nodeConfig) {
            if (nodeConfig.uType) {
                return nodeConfig.num + '个'+ nodeConfig.postionName
            } else {
                if (nodeConfig.userLst && nodeConfig.userLst.length > 0) {
                    let users = ''
                    let userList = JSON.parse(nodeConfig.userLst).users
                    for (let i in userList) {
                        if (i == userList.length - 1) {
                            users = users + userList[i].name
                        } else {
                            users = users + userList[i].name + '、'
                        }
                    }
                    console.log('测试 --->', userList, users)
                    return users
                } else {
                    // if (nodeConfig.userSelectFlag) {
                    return '请选择'
                    // } else {
                    //     return false
                    // }
                }
            }
        }
    }
}
</script>

<style></style>
