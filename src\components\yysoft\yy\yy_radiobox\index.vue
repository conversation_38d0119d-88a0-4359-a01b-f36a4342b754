<template>
    <el-row>
        <el-col :span="24">
            <el-button @click="dialogVisible = true">
                {{ buttonText }}
            </el-button>
        </el-col>
        <el-col :span="24">
            <template v-if="this.cardInfoArr.length >= 1">
                <template
                    v-for="(item, index) in this.cardInfoArr"
                    :key="index"
                >
                    <el-card
                        class="box-card"
                        shadow="never"
                    >
                        <el-descriptions
                            class="el-descriptions"
                            column="3"
                        >
                            <template
                                v-for="(itm, inx) in this.cardColums"
                                :key="inx"
                            >
                                <el-descriptions-item :label="itm.label">
                                    <descriptionsItem
                                        :form="item[itm.prop]"
                                        :formAll="item"
                                        :item="itm"
                                        :component="itm.component"
                                        :options="itm.options"
                                    ></descriptionsItem>
                                </el-descriptions-item>
                            </template>
                        </el-descriptions>
                    </el-card>
                </template>
            </template>
        </el-col>
    </el-row>
    <el-dialog
        v-model="dialogVisible"
        :title="title"
        width="70%"
        :before-close="handleClose"
    >
        <yp_list_tree
            :url="remote.api"
            :postData="remote.data"
            :columns="columns"
            :sideNav="sideNav"
            :queryItem="queryItem"
            :buttonList="buttonList"
            :radio="radio"
            @mediaList="mediaList"
        ></yp_list_tree>
    </el-dialog>
</template>

<script>
import descriptionsItem from './Items.vue'
export default {
    name: '',
    components: {
        descriptionsItem
    },
    props: {
        buttonText: { type: String, default: '请选择' },
        title: { type: String, default: '' },
        remote: { type: Object, default: () => {} },
        columns: { type: Object, default: () => {} },
        sideNav: { type: Object, default: () => {} },
        queryItem: { type: Object, default: () => {} },
        buttonList: { type: Object, default: () => {} },
        radio: { type: Boolean, default: false },
        name: { type: String, default: '' },
        form: { type: Object, default: () => {} },
        cardColums: { type: Object, default: () => {} }
    },
    data() {
        return {
            dialogVisible: false,
            cardInfoArr: []
        }
    },
    methods: {
        mediaList(array) {
            this.dialogVisible = false
            this.cardInfoArr = JSON.parse(JSON.stringify(array))
            if (this.radio) {
                this.form[this.name] = array[0]
            } else {
                this.form[this.name] = array
            }
        }
    }
}
</script>

<style scoped lang="scss">
.box-card {
    width: 700px;
    margin-top: 20px;
}
</style>