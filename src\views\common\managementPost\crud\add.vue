<template>
    <yp_form
        :columns="columns"
        :backrouter="backrouter"
    ></yp_form>
</template>

<script>
export default {
    name: 'managementPostAdd',
    data() {
        return {
            backrouter: '/common/managementPost',
            columns: [
                {
                    label: '岗位名称',
                    name: 'title',
                    component: 'input',
                    options: {
                        placeholder: '请输入'
                    },
                    rules: [
                        {
                            required: true,
                            message: '请设置分组名称',
                            trigger: 'change'
                        }
                    ]
                },
                {
                    label: '人员',
                    name: 'userlst',
                    component: 'selectUser',
                    options: {
                        tip: '不支持部门授权,请选择人员'
                    },
                    rules: [
                        {
                            required: true,
                            message: '请设置人员',
                            trigger: 'change'
                        }
                    ],
                    message: '不支持部门授权,请选择人员'
                },
            ]
        }
    }
}
</script>

<style>
</style>
