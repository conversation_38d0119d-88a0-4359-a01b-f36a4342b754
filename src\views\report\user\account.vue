<!--
 * @author: 风源
 * @name: 类名
 * @desc: 介绍
 * @LastEditTime: 2023-01-03 23:05:48
 * @FilePath: /eyc3_canyin_pc/src/views/report/user/account.vue
-->
<template>
	<yp_list
		ref="table"
		:url="url"
		row-key="id"
		:columns="columns"
		stripe
		:add="add"
		:derive="derive"
		:formitems="formitems"
	>
	</yp_list>
</template>

<script>
export default {
	name: "ReportUserAccount",
	data() {
		return {
			url: "report/get_bill_ls",
			columns: [
				{
					label: "姓名",
					prop: "user_name",
				},
				{
					label: "部门名称",
					prop: "department_name",
				},
				{
					label: "部门路径",
					prop: "dept_title_list",
				},
				{
					label: "工号",
					prop: "job_number",
				},
				{
					label: "餐厅名称",
					prop: "dininghall_title",
				},
				{
					label: "订单类型",
					prop: "type",
					component: "fillvalue",
					options: [
						"固定金额订单",
						"自定义订单",
						"报餐订单",
						"点餐订单",
						"超市购物",
						"会议餐",
					],
				},
				{
					label: "支付类型",
					prop: "pay_type",
					component: "fillvalue",
					options: ["账户余额支付", "支付宝在线支付", "微信在线支付"],
				},
				{
					label: "订单状态",
					prop: "status",
					component: "fillvalue",
					options: [
						"已报餐/已点餐",
						"已支付",
						"已派送",
						"已就餐",
						"已评价",
						"已取消",
					],
				},
				{
					label: "消费金额",
					prop: "money",
				},

				{
					label: "虚拟账户余额",
					prop: "virtual_balance",
				},
				{
					label: "真实账户余额",
					prop: "real_balance",
				},
				{
					label: "虚拟消费金额",
					prop: "virtual_money",
				},
				{
					label: "真实消费金额",
					prop: "real_money",
				},
				{
					label: "菜品详情",
					prop: "dishess",
					component: "table",
					options: [
						{
							label: "名称",
							prop: "title"
						},
						{
							label: "分类",
							prop: "category_title"
						},
						{
							label: "数量",
							prop: "count"
						},
						{
							label: "价格",
							prop: "price"
						},
					],
				},
				{
					label: "餐时",
					prop: "repast_title",
					sorter: true,
				},
				{
					label: "窗口",
					prop: "window_title",
					sorter: true,
				},
				{
					label: "成本中心",
					prop: "cost_title",
				},
				{
					label: "备注",
					prop: "desc",
				},
				{
					label: "消费时间",
					prop: "updated_at",
					sorter: true,
				},
				{
					label: "操作",
					prop: "action",
					component: "button",
					options: [
						{
							label: "退款",
							type: "warning",
							component: "confirm",
							options: {
								label: "确认退款",
								message: "是否确认退款?",
								remote: {
									api: `bill/post_refund`,
									data: {
										id:"$id"
									},
								},
							},
						},
					],
				},
			],
			derive: {
				filename:"账单明细记录"
			},
			formitems: [
				{
					label: "日期范围",
					name: "date_interval",
					value: [
						this.$TOOL.dateFormat(new Date(), "yyyy-MM-01"),
						this.$TOOL.dateFormat(new Date(), "yyyy-MM-dd"),
					],
					component: "date",
					options: {
						type: "daterange",
						rangeseparator: "至",
						startplaceholder: "开始日期",
						endplaceholder: "结束日期",
						valueFormat: "YYYY-MM-DD",
					},
					rules: [
						{
							required: true,
							message: "Please input Data",
							trigger: "change",
						},
					],
				},
				{
					label: "选择人员",
					name: "userlst",
					value: "",
					component: "selectUser"
				},
				{
					label: "餐厅",
					name: "dininghall_id",
					value: "",
					component: "select",
					options: {
						remote: {
							api: `dininghall/get_all`,
							data: { name: "b" },
							label: "title",
							value: "id",
						},
						items: [
							{
								label: "全部",
								value: "",
							},
						],
					},
				},
				{
					label: "餐时",
					name: "repast_id",
					value: "",
					component: "select",
					options: {
						remote: {
							api: `repast/get_all`,
							data: { dininghall_id: "$dininghall_id" },
							label: "title",
							value: "id",
						},
						items: [
							{
								label: "全部",
								value: "",
							},
						],
					},
				},
				{
					label: "窗口",
					name: "window_id",
					value: "",
					component: "select",
					options: {
						remote: {
							api: `window/get_all`,
							data: { dininghall_id: "$dininghall_id" },
							label: "title",
							value: "id",
						},
						items: [
							{
								label: "全部",
								value: "",
							},
						],
					},
				},
				{
					label: "类型",
					name: "type",
					value: "",
					component: "select",
					options: {
						items: [
							{
								label: "全部",
								value: "",
							},
							{
								label: "报餐订单",
								value: 2,
							},
							{
								label: "点餐订单",
								value: 3,
							},
						],
					},
				},
				{
					label: "离职人员",
					name: "isleave",
					value: false,
					component: "switch",
				},
				// {
				// 	label: "输入框",
				// 	name: "name",
				// 	value: "",
				// 	component: "input",
				// 	options: {
				// 		maxlength: "20",
				// 		placeholder: "Activity name",
				// 	},
				// 	rules: [
				// 		{required: true, message: "Please input Activity name", trigger: "blur"}
				// 	],
				// 	requiredHandle: "$.required==true",
				// },
				// {
				// 	label: "栅格(12/24)",
				// 	name: "name2",
				// 	value: "",
				// 	component: "input",
				// 	span: 12,
				// 	options: {
				// 		placeholder: "span: 12",
				// 	}
				// },
				// {
				// 	label: "栅格(12/24)",
				// 	name: "name3",
				// 	value: "",
				// 	component: "input",
				// 	span: 12,
				// 	options: {
				// 		placeholder: "span: 12",
				// 	}
				// },
				// {
				// 	label: "选择器(多选)",
				// 	name: "select",
				// 	value: "",
				// 	component: "select",
				// 	span: 24,
				// 	options: {
				// 		remote: {
				// 			api: `${this.$CONFIG.API_URL}/system/dic/get`,
				// 			data: {name: 'a'}
				// 		},
				// 		multiple: true,
				// 		items:[
				// 			{
				// 				label: "选项1",
				// 				value: "1"
				// 			},
				// 			{
				// 				label: "选项2",
				// 				value: "2"
				// 			}
				// 		]
				// 	},
				// 	rules: [
				// 		{required: true, message: "Please input Activity name", trigger: "change"}
				// 	],
				// 	requiredHandle: "$.required==true",
				// },

				// {
				// 	label: "级联选择器",
				// 	name: "cascader",
				// 	value: "",
				// 	component: "cascader",
				// 	span: 24,
				// 	options: {
				// 		items:[
				// 			{
				// 				label: "Guide",
				// 				value: "guide",
				// 				children: [
				// 					{
				// 						label: "Disciplines",
				// 						value: "disciplines"
				// 					},
				// 					{
				// 						label: "Consistency",
				// 						value: "consistency"
				// 					},
				// 				]
				// 			},
				// 			{
				// 				label: "Resource",
				// 				value: "resource",
				// 				children: [
				// 					{
				// 						label: "Axure Components",
				// 						value: "axure"
				// 					},
				// 					{
				// 						label: "Sketch Templates",
				// 						value: "sketch"
				// 					},
				// 					{
				// 						label: "Design Documentation",
				// 						value: "docs"
				// 					}
				// 				]
				// 			},
				// 			{
				// 				label: "Component",
				// 				value: "component"
				// 			},
				// 		]
				// 	}
				// },
				// {
				// 	label: "多选框",
				// 	name: "checkbox",
				// 	component: "checkbox",
				// 	span: 24,
				// 	tips: "多选框配置加上 name 表示拥有嵌套关系。否则将值“平铺”在form对象",
				// 	options: {
				// 		items:[
				// 			{
				// 				label: "选项1",
				// 				name: "option1",
				// 				value: false
				// 			},
				// 			{
				// 				label: "选项2",
				// 				name: "option2",
				// 				value: false
				// 			}
				// 		]
				// 	},
				// 	hideHandle: "$.required==true"
				// },
				// {
				// 	label: "多选框组",
				// 	name: "checkboxGroup",
				// 	value: [],
				// 	component: "checkboxGroup",
				// 	span: 24,
				// 	options: {
				// 		items:[
				// 			{
				// 				label: "选项1",
				// 				value: "option1"
				// 			},
				// 			{
				// 				label: "选项2",
				// 				value: "option2"
				// 			}
				// 		]
				// 	},
				// 	hideHandle: "$.required==true"
				// },
				// {
				// 	label: "单选",
				// 	name: "radio",
				// 	value: "1",
				// 	component: "radio",
				// 	options: {
				// 		items:[
				// 			{
				// 				label: "选项1",
				// 				value: "1"
				// 			},
				// 			{
				// 				label: "选项2",
				// 				value: "2"
				// 			}
				// 		]
				// 	},
				// 	hideHandle: "$.required==true"
				// },
				// {
				// 	label: "开关",
				// 	name: "required",
				// 	message: "演示如何使用表达式动态显隐和必填，试试打开和关闭开关",
				// 	value: false,
				// 	component: "switch",
				// },

				// {
				// 	label: "滑块",
				// 	name: "slider",
				// 	value: 0,
				// 	component: "slider",
				// 	options: {
				// 		marks: {
				// 			0: '0°C',
				// 			8: '8°C',
				// 			37: '37°C'
				// 		}
				// 	}
				// },
				// {
				// 	label: "数值",
				// 	name: "number",
				// 	value: 0,
				// 	component: "number",
				// },
				// {
				// 	label: "颜色",
				// 	name: "color",
				// 	value: "",
				// 	component: "color",
				// },
				// {
				// 	label: "评分",
				// 	name: "rate",
				// 	value: 0,
				// 	component: "rate",
				// },
				// {
				// 	label: "SCUI扩展",
				// 	component: "title",
				// },
				// {
				// 	label: "表格选择器",
				// 	name: "tableselect",
				// 	value: {},
				// 	component: "tableselect",
				// 	span: 24,
				// 	options: {
				// 		apiObj: '$API.demo.page',
				// 		column: [
				// 			{
				// 				label: "ID",
				// 				prop: "id",
				// 				width:150
				// 			},
				// 			{
				// 				label: "姓名",
				// 				prop: "user"
				// 			}
				// 		],
				// 		props: {
				// 			label: 'user',
				// 			value: 'id',
				// 			keyword: "keyword"
				// 		}
				// 	}
				// },
				// {
				// 	label: "上传",
				// 	component: "upload",
				// 	options: {
				// 		items:[
				// 			{
				// 				label: "图像1",
				// 				name: "img1",
				// 				value: ""
				// 			},
				// 			{
				// 				label: "图像2",
				// 				name: "img2",
				// 				value: ""
				// 			}
				// 		]
				// 	}
				// },
				// {
				// 	label: "富文本",
				// 	name: "text",
				// 	value: "",
				// 	component: "editor",
				// },
			],
		};
	},
};
</script>

<style></style>
