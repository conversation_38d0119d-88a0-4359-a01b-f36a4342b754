<template>
    <promoter
        v-if="nodeConfig.type == 0"
        v-model="nodeConfig"
        :isRecommend="isRecommend"
        :recommendOptions.sync="recommendOptions"
    ></promoter>

    <approver
        v-if="nodeConfig.type == 1"
        v-model="nodeConfig"
        :isRecommend="isRecommend"
        :recommendOptions.sync="recommendOptions"
    ></approver>

    <send
        v-if="nodeConfig.type == 2"
        v-model="nodeConfig"
        :isRecommend="isRecommend"
        :recommendOptions.sync="recommendOptions"
        :postItems="postItems"
        
    ></send>

    <branch
        v-if="nodeConfig.type == 4"
        v-model="nodeConfig"
        :isRecommend="isRecommend"
        :recommendOptions.sync="recommendOptions"
        :postItems="postItems"
    >
        <template v-slot="slot">
            <node-wrap
                v-if="slot.node"
                v-model="slot.node.childNode"
                :isRecommend="isRecommend"
                :recommendOptions.sync="recommendOptions"
                :postItems="postItems"
            ></node-wrap>
        </template>
    </branch>

    <node-wrap
        v-if="nodeConfig.childNode"
        v-model="nodeConfig.childNode"
        :isRecommend="isRecommend"
        :recommendOptions.sync="recommendOptions"
        :postItems="postItems"
    ></node-wrap>
</template>

<script>
import approver from './nodes/approver'
import promoter from './nodes/promoter'
import branch from './nodes/branch'
import send from './nodes/send'

export default {
    props: {
        modelValue: { type: Object, default: () => {} },
        isRecommend: { type: Boolean, default: false },
        recommendOptions: { type: Object, default: () => {} },
        postItems: { type: Array, default: () => [] }
    },
    components: {
        approver,
        promoter,
        branch,
        send
    },
    data() {
        return {
            nodeConfig: {}
        }
    },
    watch: {
        modelValue(val) {
            this.nodeConfig = val
        },
        nodeConfig(val) {
            this.$emit('update:modelValue', val)
        }
    },
    mounted() {
        this.nodeConfig = this.modelValue
        console.log('测试2222', this.postItems)
    },
    methods: {}
}
</script>

<style>
</style>
