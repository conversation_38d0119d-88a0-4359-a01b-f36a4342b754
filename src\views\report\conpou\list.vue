<!--
 * @author: 风源
 * @name: 类名
 * @desc: 介绍
 * @LastEditTime: 2022-12-12 20:50:54
 * @FilePath: \eyc3_pc_base\src\views\report\conpou\list.vue
-->
<template>
    <yp_list
        ref="table"
        :url="url"
        row-key="id"
        :columns="columns"
        stripe
        :add="add"
        :derive="derive"
        :formitems="formitems"
    >
    </yp_list>
</template>

<script>
export default {
    name: 'ReportConpouList',
    data() {
        return {
            url: 'report/get_bill_ls',
            columns: [
                {
                    label: '姓名',
                    prop: 'user_name'
                },
                {
                    label: '部门名称',
                    prop: 'department_name'
                },

                {
                    label: '就餐餐厅',
                    prop: 'dininghall_title'
                },
                {
                    label: '金额',
                    prop: 'money'
                },
                {
                    label: '优惠金额',
                    prop: 'coupon_money'
                },

                {
                    label: '虚拟账户付款',
                    prop: 'virtual_money'
                },
                {
                    label: '虚拟账户余额',
                    prop: 'virtual_balance'
                },
                {
                    label: '真实账户付款',
                    prop: 'real_money'
                },
                {
                    label: '真实账户余额',
                    prop: 'real_balance'
                },
                {
                    label: '补贴',
                    prop: 'subsidy_money'
                },
                {
                    label: '餐时',
                    prop: 'repast_title',
                },
                {
                    label: '窗口',
                    prop: 'window_title',
                },
                {
                    label: '菜品详情',
                    prop: 'dishess',
					component: "table",
					options: [
						{
							label: "名称",
							prop: "title"
						},
						{
							label: "分类",
							prop: "category_title"
						},
						{
							label: "数量",
							prop: "count"
						},
						{
							label: "价格",
							prop: "price"
						},
					],
                },
                {
                    label: '备注',
                    prop: 'desc'
                },
                {
                    label: '消费时间',
                    prop: 'date',
                }
            ],
            derive: {
                filename:"优惠券账单"
            },
            formitems: [
                {
                    label: '日期范围',
                    name: 'date_interval',
                    value: [
                        this.$TOOL.dateFormat(new Date(), 'yyyy-MM-01'),
                        this.$TOOL.dateFormat(new Date(), 'yyyy-MM-dd')
                    ],
                    component: 'date',
                    options: {
                        type: 'daterange',
                        rangeseparator: '至',
                        startplaceholder: '开始日期',
                        endplaceholder: '结束日期',
                        valueFormat: 'YYYY-MM-DD'
                    },
                    rules: [
                        {
                            required: true,
                            message: 'Please input Data',
                            trigger: 'change'
                        }
                    ]
                },
                {
					label: "选择人员",
					name: "userlst",
					value: "",
					component: "selectUser"
				},
				{
                    label: '优惠劵',
                    name: 'coupon_id',
                    value: '',
                    component: 'select',
                    options: {
                        remote: {
                            api: `coupon/get_all`,
                            label: 'title',
                            value: 'id'
                        },
                        items: [
                            {
                                label: '全部',
                                value: ''
                            }
                        ]
                    }
                },
                {
                    label: '餐厅',
                    name: 'dininghall_id',
                    value: '',
                    component: 'select',
                    options: {
                        remote: {
                            api: `dininghall/get_all`,
                            data: { name: 'b' },
                            label: 'title',
                            value: 'id'
                        },
                        items: [
                            {
                                label: '全部',
                                value: ''
                            }
                        ]
                    }
                },
                {
                    label: '餐时',
                    name: 'repast_id',
                    value: '',
                    component: 'select',
                    options: {
                        remote: {
                            api: `repast/get_all`,
                            data: { dininghall_id: '$dininghall_id' },
                            label: 'title',
                            value: 'id'
                        },
                        items: [
                            {
                                label: '全部',
                                value: ''
                            }
                        ]
                    }
                },
                {
                    label: '窗口',
                    name: 'window_id',
                    value: '',
                    component: 'select',
                    options: {
                        remote: {
                            api: `window/get_all`,
                            data: { dininghall_id: '$dininghall_id' },
                            label: 'title',
                            value: 'id'
                        },
                        items: [
                            {
                                label: '全部',
                                value: ''
                            }
                        ]
                    }
                },
                {
                    label: '离职人员',
                    name: 'isleave',
                    value: false,
                    component: 'switch'
                }
                
            ]
        }
    }
}
</script>

<style></style>
