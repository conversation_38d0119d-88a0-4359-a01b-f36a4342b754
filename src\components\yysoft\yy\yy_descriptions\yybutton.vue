<template>
    <el-button
        :type="type"
        @click="onclick"
        :icon="list"
        :loading="loading"
        >{{ label }}</el-button
    >
</template>
<script>
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
export default {
    name: 'yy_button',
    emits: ['finishEvent', 'dialog'],
    props: {
        label: { type: String, default: '' },
        type: { type: String, default: 'primary' },
        component: { type: String, default: '' },
        options: { type: Object, default: () => null },
        value: { type: Object, default: () => null },
        queryform: { type: Object, default: () => {} },
        formAll: { type: Object, default: () => {} }
        // multiple: { type: Boolean, default: false },
        // placeholder: { type: String, default: "" },
        // remote: { type: Object, default: () => null },
        // items: { type: Object, default: () => null },
    },
    data() {
        return {
        }
    },
    mounted() {
    },
    methods:{
        onclick(){
            console.log(this.options.remote.state);
            if(this.options.remote.state=='modify'){
                let postData = {
                    id:this.formAll.id
                }
                this.$HTTP.get(this.options.remote.api, Object.assign(postData,this.options.remote.data)).then((res) => {
                    if (res.errcode != 0) {
                        ElMessage.error(res.errmsg)
                    } else {
                        ElMessage.success('操作成功!')
                        this.$emit('finishEvent', true)
                    }
                    this.oncancel()
                })
            }
            console.log(this.formAll);
        }
        
    }
}

</script>