<!--
 * @Descripttion: 数据表格组件
 * @version: 1.10
 * @Author: sakuya
 * @Date: 2021年11月29日21:51:15
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-01-12 18:47:48
-->

<template>
    <el-container>
        <el-header height="auto" style="display: block">
            <yy_queryform v-model="queryform" label-width="120px" ref="formRef" :filed="formitems">
                <div class="button_lst">
                    <div class="button_left">
                        <div v-for="(item, index) in buttonList" :key="index" class="button_left_div">
                            <yy_upload
                                v-if="item.component == 'upload'"
                                :label="item.label"
                                :templateUrl="item.templateUrl"
                                :columnData="item.columnData"
                                :url="item.url"
                                :maxSize="item.maxSize"
                                :downUrl="item.downUrl"
                                :filename="item.filename"
                            ></yy_upload>
                            <yy_button
                                v-if="item.component == 'form'"
                                :label="item.label"
                                :type="item.type"
                                :options="item.options"
                                :value="query"
                                :component="item.component"
                            ></yy_button>
                            <yy_button
                                v-if="item.component == 'confirm'"
                                :label="item.label"
                                :type="item.type"
                                :options="item.options"
                                :value="query"
                                :component="item.component"
                            ></yy_button>
                        </div>
                    </div>
                    <div class="button-right">
                        <el-button type="primary" @click="refresh" icon="el-icon-Search">查询</el-button>
                        <el-button plain @click="resetForm">重置</el-button>
                        <yy_export
                            v-if="derive"
                            :url="derive.url"
                            showData
                            :column="userColumn"
                            :fileTypes="['xlsx']"
                            :query="queryform"
                            :fileName="derive.filename"
                            :showsummary="showsummary"
                        >
                        </yy_export>
                    </div>
                </div>
            </yy_queryform>
        </el-header>
        <el-main class="nopadding">
            <div class="scTable" :style="{ height: _height }" ref="scTableMain" v-loading="loading">
                <div class="scTable-table" :style="{ height: _table_height }">
                    <el-table
                        v-bind="$attrs"
                        :data="tableData"
                        :row-key="rowKey"
                        :key="toggleIndex"
                        ref="scTable"
                        :height="height == 'auto' ? null : '100%'"
                        :border="true"
                        :stripe="true"
                        @sort-change="sortChange"
                        @filter-change="filterChange"
                        :show-summary="showsummary"
                        id="yytable"
                    >
                        <!-- <el-table-column type="selection"></el-table-column> -->

                        <template v-for="(item, index) in userColumn" :key="index">
                            <el-table-column
                                v-if="item.children"
                                :label="item.label"
                                :width="item.width"
                                header-align="center"
                            >
                                <el-table-column
                                    v-for="(rs, rs_index) in item.children"
                                    :key="rs_index"
                                    :column-key="rs.prop"
                                    :label="rs.label"
                                    :prop="rs.prop"
                                    :width="rs.width"
                                    :sortable="rs.sortable"
                                    :fixed="rs.fixed"
                                    :filters="rs.filters"
                                    :filter-method="remoteFilter || !rs.filters ? null : filterHandler"
                                    :show-overflow-tooltip="rs.showOverflowTooltip"
                                >
                                    <template #default="scope">
                                        <tableslot
                                            :item="rs"
                                            :scope="scope"
                                            @finish-event="tableslot_finish_event"
                                        ></tableslot>
                                    </template>
                                </el-table-column>
                            </el-table-column>
                            <el-table-column
                                v-if="!item.hide && !item.children"
                                :column-key="item.prop"
                                :label="item.label"
                                :prop="item.prop"
                                :width="item.width"
                                :sortable="item.sortable"
                                :fixed="item.fixed"
                                :filters="item.filters"
                                :filter-method="remoteFilter || !item.filters ? null : filterHandler"
                                :show-overflow-tooltip="item.showOverflowTooltip"
                            >
                                <template #default="scope">
                                    <tableslot
                                        :item="item"
                                        :scope="scope"
                                        @finish-event="tableslot_finish_event"
                                    ></tableslot>
                                </template>
                            </el-table-column>
                        </template>
                        <el-table-column min-width="1"></el-table-column>
                        <template #empty>
                            <el-empty :description="emptyText" :image-size="100"></el-empty>
                        </template>
                    </el-table>
                </div>
                <div class="scTable-page" v-if="!hidePagination || !hideDo">
                    <div class="scTable-pagination">
                        <el-pagination
                            v-if="!hidePagination && paging"
                            background
                            :small="true"
                            :layout="paginationLayout"
                            :total="total"
                            :page-size="query.per_page"
                            :page-sizes="pageSizes"
                            v-model:currentPage="query.page"
                            @current-change="paginationChange"
                            @update:page-size="pageSizeChange"
                        ></el-pagination>
                    </div>
                    <div class="scTable-do" v-if="!hideDo">
                        <el-button
                            v-if="!hideRefresh"
                            @click="refresh"
                            icon="el-icon-refresh"
                            circle
                            style="margin-left: 15px"
                        ></el-button>
                        <el-popover
                            v-if="userColumn"
                            placement="top"
                            title="列设置"
                            :width="500"
                            trigger="click"
                            :hide-after="0"
                            @show="customColumnShow = true"
                            @after-leave="customColumnShow = false"
                        >
                            <template #reference>
                                <el-button icon="el-icon-set-up" circle style="margin-left: 15px"></el-button>
                            </template>
                            <columnSetting
                                v-if="customColumnShow"
                                ref="columnSetting"
                                @userChange="columnSettingChange"
                                @save="columnSettingSave"
                                @back="columnSettingBack"
                                :column="userColumn"
                            ></columnSetting>
                        </el-popover>
                    </div>
                </div>
            </div>
        </el-main>
    </el-container>
</template>

<script>
import config from '@/config/table'
import columnSetting from './columnSetting'
import tableslot from './tableslot'

export default {
    name: 'yp_list',
    components: {
        columnSetting,
        tableslot
    },
    props: {
        add: { type: Object, default: () => null },
        del: { type: Object, default: () => null },
        derive: { type: Object, default: () => null },
        columns: { type: Object, default: () => {} },
        url: { type: String, default: '' },
        formitems: { type: Object, default: () => {} },
        paging: { type: Boolean, default: true },
        showsummary: { type: Boolean, default: false },
        buttonList: { type: Object, default: () => {} },
        post_data: { type: Object, default: () => {} },

        tableName: { type: String, default: '' },
        params: { type: Object, default: () => ({}) },
        data: { type: Object, default: () => {} },
        height: { type: [String, Number], default: '100%' },
        size: { type: String, default: 'default' },
        border: { type: Boolean, default: true },
        stripe: { type: Boolean, default: true },
        pageSize: { type: Number, default: config.pageSize },
        pageSizes: { type: Array, default: config.pageSizes },
        rowKey: { type: String, default: '' },
        remoteSort: { type: Boolean, default: false },
        remoteFilter: { type: Boolean, default: false },
        remoteSummary: { type: Boolean, default: false },
        hidePagination: { type: Boolean, default: false },
        hideDo: { type: Boolean, default: false },
        hideRefresh: { type: Boolean, default: false },
        hideSetting: { type: Boolean, default: false },
        paginationLayout: { type: String, default: config.paginationLayout }
    },
    watch: {
        //监听从props里拿到值了
        data() {
            this.tableData = this.data
            this.total = this.tableData.length
        },
        apiObj() {
            this.tableParams = this.params
            this.refresh()
        }
        // $route(to, from) {
        //     console.log('toooooo', to)
        // },
        // columns: {
        //     handle(val,oldval) {
        //         console.log(val,ol)
        //     },
        //     immediate: true
        // }
    },
    computed: {
        _height() {
            return Number(this.height) ? Number(this.height) + 'px' : this.height
        },
        _table_height() {
            return this.hidePagination && this.hideDo ? '100%' : 'calc(100% - 50px)'
        }
    },
    data() {
        return {
            userColumn: [],
            queryform: {},
            query: {
                download: 0,
                per_page: 10,
                page: 1
                // date_interval:"[\"2022-12-01\",\"2022-12-08\"]"
            },

            isActivat: true,
            emptyText: '暂无数据',
            toggleIndex: 0,
            tableData: [],
            total: 0,
            prop: null,
            order: null,
            loading: false,
            tableHeight: '100%',
            tableParams: this.params,
            customColumnShow: false,
            summary: {},
            config: {
                size: this.size,
                border: this.border,
                stripe: this.stripe
            }
        }
    },
    created() {
        console.log(this.paging)
    },
    mounted() {
        //判断是否开启自定义列
        this.userColumn = this.columns
        // this.$TOOL.deep_merge(this, this.set);
        //获取数据
        this.getData()
    },
    activated() {
        if (!this.isActivat) {
            this.$refs.scTable.doLayout()
        }
        // console.log('activaaaaaaaaa')
        // this.refresh()
    },
    deactivated() {
        this.isActivat = false
    },
    methods: {
        async getData() {
            this.loading = true
            //获取动态表头
            this.userColumn.map((n) => {})

            //获取数据
            this.$HTTP
                .get(this.url, { ...this.query, ...this.queryform, ...this.post_data })
                .then((res) => {
                    console.log(res)
                    if (res.errcode != 0) {
                        this.loading = false
                        this.emptyText = res.errmsg
                    } else {
                        this.emptyText = '暂无数据'
                        if (this.paging) {
                            this.tableData = res.result.data || []
                            this.total = res.result.total || 0
                        } else {
                            this.tableData = res.result || []
                        }
                        this.loading = false
                    }
                    this.$refs.scTable.setScrollTop(0)
                    this.$emit('dataChange', res, this.tableData)
                })
                .finally(() => {
                    this.modalLoading = false
                })
        },
        //分页点击
        paginationChange() {
            console.log(this.query.page)
            this.getData()
        },
        //条数变化
        pageSizeChange(size) {
            this.query.per_page = size
            this.getData()
        },
        //刷新数据
        refresh() {
            console.log(222111333)
            this.$refs.scTable.clearSelection()
            this.getData()
        },
        //重置表单
        resetForm() {
            this.$refs.formRef.resetFields()
            // console.log(formEl)
            // if (!formEl) return
            // formEl.resetFields()
        },
        //更新数据 合并上一次params
        upData(params, page = 1) {
            this.query.page = page
            this.$refs.scTable.clearSelection()
            Object.assign(this.tableParams, params || {})
            this.getData()
        },
        //重载数据 替换params
        reload(params, page = 1) {
            this.query.page = page
            this.tableParams = params || {}
            this.$refs.scTable.clearSelection()
            this.$refs.scTable.clearSort()
            this.$refs.scTable.clearFilter()
            this.getData()
        },
        //自定义变化事件
        columnSettingChange(userColumn) {
            this.userColumn = userColumn
            this.toggleIndex += 1
        },
        //自定义列保存
        async columnSettingSave(userColumn) {
            this.$refs.columnSetting.isSave = true
            try {
                await config.columnSettingSave(this.tableName, userColumn)
            } catch (error) {
                this.$message.error('保存失败')
                this.$refs.columnSetting.isSave = false
            }
            this.$message.success('保存成功')
            this.$refs.columnSetting.isSave = false
        },
        //自定义列重置
        async columnSettingBack() {
            this.$refs.columnSetting.isSave = true
            try {
                const column = await config.columnSettingReset(this.tableName, this.columns)
                this.userColumn = column
                this.$refs.columnSetting.usercolumn = JSON.parse(JSON.stringify(this.columns || []))
            } catch (error) {
                this.$message.error('重置失败')
                this.$refs.columnSetting.isSave = false
            }
            this.$refs.columnSetting.isSave = false
        },
        //排序事件
        sortChange(obj) {
            if (!this.remoteSort) {
                return false
            }
            if (obj.columns && obj.prop) {
                this.prop = obj.prop
                this.order = obj.order
            } else {
                this.prop = null
                this.order = null
            }
            this.getData()
        },
        //本地过滤
        filterHandler(value, row, column) {
            const property = column.property
            return row[property] === value
        },
        //过滤事件
        filterChange(filters) {
            if (!this.remoteFilter) {
                return false
            }
            Object.keys(filters).forEach((key) => {
                filters[key] = filters[key].join(',')
            })
            this.upData(filters)
        },
        configSizeChange() {
            this.$refs.scTable.doLayout()
        },
        //插入行 unshiftRow
        unshiftRow(row) {
            this.tableData.unshift(row)
        },
        //插入行 pushRow
        pushRow(row) {
            this.tableData.push(row)
        },
        //根据key覆盖数据
        updateKey(row, rowKey = this.rowKey) {
            this.tableData
                .filter((item) => item[rowKey] === row[rowKey])
                .forEach((item) => {
                    Object.assign(item, row)
                })
        },
        //根据index覆盖数据
        updateIndex(row, index) {
            Object.assign(this.tableData[index], row)
        },
        //根据index删除
        removeIndex(index) {
            this.tableData.splice(index, 1)
        },
        //根据index批量删除
        removeIndexes(indexes = []) {
            indexes.forEach((index) => {
                this.tableData.splice(index, 1)
            })
        },
        //根据key删除
        removeKey(key, rowKey = this.rowKey) {
            this.tableData.splice(
                this.tableData.findIndex((item) => item[rowKey] === key),
                1
            )
        },
        //根据keys批量删除
        removeKeys(keys = [], rowKey = this.rowKey) {
            keys.forEach((key) => {
                this.tableData.splice(
                    this.tableData.findIndex((item) => item[rowKey] === key),
                    1
                )
            })
        },
        //原生方法转发
        clearSelection() {
            this.$refs.scTable.clearSelection()
        },
        toggleRowSelection(row, selected) {
            this.$refs.scTable.toggleRowSelection(row, selected)
        },
        toggleAllSelection() {
            this.$refs.scTable.toggleAllSelection()
        },
        toggleRowExpansion(row, expanded) {
            this.$refs.scTable.toggleRowExpansion(row, expanded)
        },
        setCurrentRow(row) {
            this.$refs.scTable.setCurrentRow(row)
        },
        clearSort() {
            this.$refs.scTable.clearSort()
        },
        clearFilter(columnKey) {
            this.$refs.scTable.clearFilter(columnKey)
        },
        doLayout() {
            this.$refs.scTable.doLayout()
        },
        sort(prop, order) {
            this.$refs.scTable.sort(prop, order)
        },
        tableslot_finish_event() {
            console.log(99999999999)
            this.refresh()
        }
    }
}
</script>

<style scoped>
.scTable {
}
.scTable-table {
    height: calc(100% - 50px);
}
.scTable-page {
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
}
.scTable-do {
    white-space: nowrap;
}
.scTable:deep(.el-table__footer) .cell {
    font-weight: bold;
}
.scTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-horizontal {
    height: 12px;
    border-radius: 12px;
}
.scTable:deep(.el-table__body-wrapper) .el-scrollbar__bar.is-vertical {
    width: 12px;
    border-radius: 12px;
}
.button_lst {
    display: flex;
    justify-content: space-between;
}
.button_left {
    display: flex;
}
.button_left_div {
    margin-right: 15px;
}
</style>
