<!--
 * @Descripttion: 动态表单渲染器
 * @version: 1.0
 * @Author: sakuya
 * @Date: 2021年9月22日09:26:25
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-12-08 23:49:58
-->

<template>
    <el-skeleton
        v-if="renderLoading || Object.keys(form).length == 0"
        animated
    />
    <el-form
        v-else
        ref="form"
        :rules="rules"
        :model="form"
        :label-width="labelWidth"
        labelPosition="left"
    >
        <el-row :gutter="15">
            <template
                v-for="(item, index) in filed"
                :key="index"
            >
                <el-col
                    :span="item.span || 24"
                    v-if="!hideHandle(item)"
                >
                    <!-- <sc-title
                        v-if="item.component == 'title'"
                        :title="item.label"
                    ></sc-title> -->
                    <div
                        class="form-title"
                        v-if="item.component == 'title'"
                    >
                        {{ item.label }}
                    </div>
                    <!-- 第一层 -->
                    <template v-else-if="!item.children">
                        <el-form-item
                            :prop="item.name"
                            :rules="item.rules"
                        >
                            <template #label>
                                {{ item.label }}
                                <el-tooltip
                                    v-if="item.tips"
                                    :content="item.tips"
                                >
                                    <el-icon><el-icon-question-filled /></el-icon>
                                </el-tooltip>
                            </template>
                            <template v-if="item.component">
                                <formslot
                                    :item="item"
                                    :form="form"
                                    @changeremoteitems="changeremoteitems"
                                ></formslot>
                            </template>
                            <div
                                v-if="item.message"
                                class="el-form-item-msg"
                            >
                                {{ item.message }}
                            </div>
                        </el-form-item>
                    </template>
                    <template v-else-if="item.children">
                        <el-form-item
                            :prop="item.name"
                            :rules="item.rules"
                        >
                            <template #label>
                                {{ item.label }}
                                <el-tooltip
                                    v-if="item.tips"
                                    :content="item.tips"
                                >
                                    <el-icon><el-icon-question-filled /></el-icon>
                                </el-tooltip>
                            </template>
                            <template v-if="item.component">
                                <formslot
                                    :item="item"
                                    :form="form"
                                    @changeremoteitems="changeremoteitems"
                                ></formslot>
                            </template>
                            <div
                                v-if="item.message"
                                class="el-form-item-msg"
                            >
                                {{ item.message }}
                            </div>
                        </el-form-item>
                        <template
                            v-for="(itm, inx) in item.children"
                            :key="inx"
                        >
                            <!-- 第二层 -->
                            <template v-if="!itm.children">
                                <el-form-item
                                    :prop="itm.name"
                                    :rules="itm.rules"
                                    v-if="!itm.options.merge"
                                >
                                    <template #label>
                                        <div class="secondarr"></div>
                                        {{ itm.label }}
                                        <el-tooltip
                                            v-if="itm.tips"
                                            :content="itm.tips"
                                        >
                                            <el-icon><el-icon-question-filled /></el-icon>
                                        </el-tooltip>
                                    </template>
                                    <template v-if="itm.component">
                                        <formslot
                                            :item="itm"
                                            :form="form"
                                            @changeremoteitems="changeremoteitems"
                                        ></formslot>
                                    </template>
                                    <div
                                        v-if="itm.message"
                                        class="el-form-item-msg"
                                    >
                                        {{ itm.message }}
                                    </div>
                                </el-form-item>
                            </template>
                            <template v-else-if="itm.children">
                                <el-form-item
                                    :prop="itm.name"
                                    :rules="itm.rules"
                                    v-if="!itm.options.merge"
                                >
                                    <template #label>
                                        <div class="secondarr"></div>
                                        {{ itm.label }}
                                        <el-tooltip
                                            v-if="itm.tips"
                                            :content="itm.tips"
                                        >
                                            <el-icon><el-icon-question-filled /></el-icon>
                                        </el-tooltip>
                                    </template>
                                    <template v-if="itm.component">
                                        <formslot
                                            :item="itm"
                                            :form="form"
                                            @changeremoteitems="changeremoteitems"
                                        ></formslot>
                                    </template>
                                    <div
                                        v-if="itm.message"
                                        class="el-form-item-msg"
                                    >
                                        {{ itm.message }}
                                    </div>
                                </el-form-item>
                                <template
                                    v-for="(im, ix) in itm.children"
                                    :key="ix"
                                >
                                    <!-- 第三层 -->
                                    <template v-if="!im.children">
                                        <el-form-item
                                            :prop="im.name"
                                            :rules="im.rules"
                                            v-if="!im.options.merge"
                                        >
                                            <template #label>
                                                <div class="secondarr"></div>
                                                {{ im.label }}
                                                <el-tooltip
                                                    v-if="im.tips"
                                                    :content="im.tips"
                                                >
                                                    <el-icon><el-icon-question-filled /></el-icon>
                                                </el-tooltip>
                                            </template>
                                            <template v-if="im.component">
                                                <formslot
                                                    :item="im"
                                                    :form="form"
                                                    @changeremoteitems="changeremoteitems"
                                                ></formslot>
                                            </template>
                                            <div
                                                v-if="im.message"
                                                class="el-form-item-msg"
                                            >
                                                {{ im.message }}
                                            </div>
                                        </el-form-item>
                                    </template>
                                    <template v-else-if="im.children">
                                        <el-form-item
                                            :prop="im.name"
                                            :rules="im.rules"
                                            v-if="!im.options.merge"
                                        >
                                            <template #label>
                                                <div class="secondarr"></div>
                                                {{ im.label }}
                                                <el-tooltip
                                                    v-if="im.tips"
                                                    :content="im.tips"
                                                >
                                                    <el-icon><el-icon-question-filled /></el-icon>
                                                </el-tooltip>
                                            </template>
                                            <template v-if="im.component">
                                                <formslot
                                                    :item="im"
                                                    :form="form"
                                                    @changeremoteitems="changeremoteitems"
                                                ></formslot>
                                            </template>
                                            <div
                                                v-if="im.message"
                                                class="el-form-item-msg"
                                            >
                                                {{ im.message }}
                                            </div>
                                        </el-form-item>
                                        <template
                                            v-for="(_im, _ix) in im.children"
                                            :key="_ix"
                                        >
                                            <el-form-item
                                                :prop="_im.name"
                                                :rules="_im.rules"
                                                v-if="!_im.options.merge"
                                            >
                                                <template #label>
                                                    <div class="secondarr"></div>
                                                    {{ _im.label }}
                                                    <el-tooltip
                                                        v-if="_im.tips"
                                                        :content="_im.tips"
                                                    >
                                                        <el-icon><el-icon-question-filled /></el-icon>
                                                    </el-tooltip>
                                                </template>
                                                <template v-if="_im.component">
                                                    <formslot
                                                        :item="_im"
                                                        :form="form"
                                                        @changeremoteitems="changeremoteitems"
                                                    ></formslot>
                                                </template>
                                                <div
                                                    v-if="_im.message"
                                                    class="el-form-item-msg"
                                                >
                                                    {{ _im.message }}
                                                </div>
                                            </el-form-item>
                                        </template>
                                    </template>
                                </template>
                            </template>
                        </template>
                    </template>
                </el-col>
            </template>
            <!-- bottombtn -->
            <el-col :span="24">
                <el-form-item>
                    <slot name="bottombtn">
                        <el-button
                            v-if="status == 'add' || status == 'edit'"
                            type="primary"
                            @click="submit"
                            :loading="subloading"
                            >提交</el-button
                        >
                        <el-button
                            v-if="status == 'add'"
                            type="info"
                            @click="resetFields"
                            >重置</el-button
                        >
                        <yy_export
                            v-if="derive"
                            :url="derive.url"
                            :fileName="derive.filename"
                            showData
                            :column="columns"
                            :fileTypes="['xlsx']"
                            :dynamicColumns="dynamicColumns"
                            :query="form"
                            :showsummary="showsummary"
                        ></yy_export>
                        <el-button @click="goBack">返回</el-button>
                    </slot>
                </el-form-item>
            </el-col>
        </el-row>
    </el-form>
</template>

<script>
import formslot from './items/formslot.vue'

import { defineAsyncComponent } from 'vue'
import yy_tree from '@/components/yysoft/yy/yy_tree'
import yy_selectitems from '@/components/yysoft/yy/yy_selectitems'
import { number } from 'echarts'
const tableselectRender = defineAsyncComponent(() => import('./items/tableselect'))
const scEditor = defineAsyncComponent(() => import('@/components/scEditor'))

export default {
    name: 'yy_form',
    components:{
        yy_tree,
        yy_selectitems
    },
    props: {
        filed: { type: Object, default: () => {} },
        modelValue: { type: Object, default: () => {} },
        labelWidth: { type: String, default: '100px' },
        // 表单loading
        loading: { type: Boolean, default: false },
        // 表单状态
        status: { type: String, default: '' },
        // 导出配置
        derive: { type: Object, default: () => {} }
    },
    emits: ['submit', 'goBack', 'changeremoteitems', 'slotsubmitemit'],
    components: {
        tableselectRender,
        scEditor,
        formslot
    },
    data() {
        return {
            form: {},
            renderLoading: false,
            formitems: [],
            // 提交按钮loading
            subloading: false
        }
    },
    watch: {
        modelValue() {
            // console.log(this.modelValue,'this.modelValue12312312');
            if (this.hasConfig) {
                this.form = this.modelValue
                this.deepMerge(this.form, this.modelValue)
            }
        },
        config() {
            this.render()
        },
        form: {
            handler(val) {
                this.handlerformitems(val)
            },
            deep: true
        }
    },
    computed: {
        hasConfig() {
            // console.log(this.filed)
            return Object.keys(this.filed).length > 0
        },
        hasValue() {
            return Object.keys(this.modelValue).length > 0
        }
    },
    mounted() {
        if (this.hasConfig) {
            this.render()
        }
        // this.handlerformitems(this.form)
    },
    methods: {
        //构建form对象
        render() {
            // console.log(this.filed)
            // this.renderLoading = true
            this.filed.forEach((item) => {
                if (item.component == 'checkbox') {
                    if (item.name) {
                        const value = {}
                        item.options.items.forEach((option) => {
                            value[option.name] = option.value
                        })
                        this.form[item.name] = value
                    } else {
                        item.options.items.forEach((option) => {
                            this.form[option.name] = option.value
                        })
                    }
                } else if (item.component == 'upload') {
                    if (item.name) {
                        const value = {}
                        item.options.items.forEach((option) => {
                            value[option.name] = option.value
                        })
                        this.form[item.name] = value
                    } else {
                        item.options.items.forEach((option) => {
                            this.form[option.name] = option.value
                        })
                    }
                } else {
                    this.form[item.name] = item.value
                }
            })
            if (this.hasValue) {
                this.form = this.deepMerge(this.form, this.modelValue)
                // console.log(this.form, this.modelValue, 'this.form, this.modelValue')
            }
            // this.getData()
            // Promise.all(remoteData).then(()=>{
            // 	this.renderLoading = false
            // })
        },
        //处理远程选项数据
        // getData() {
        //     this.renderLoading = true
        //     var remoteData = []
        //     this.filed.forEach((item) => {
        //         if (item.options && item.options.remote) {
        //             var req = http.get(item.options.remote.api, item.options.remote.data).then((res) => {
        //                 item.options.remoteitems = res.data
        //             })
        //             remoteData.push(req)
        //         }
        //     })
        //     Promise.all(remoteData).then(() => {
        //         this.renderLoading = false
        //     })
        // },
        //合并深结构对象
        deepMerge(obj1, obj2) {
            let key
            for (key in obj2) {
                obj1[key] =
                    obj1[key] &&
                    obj1[key].toString() === '[object Object]' &&
                    obj2[key] &&
                    obj2[key].toString() === '[object Object]'
                        ? this.deepMerge(obj1[key], obj2[key])
                        : (obj1[key] = obj2[key])
            }
            return obj1
        },
        //数据验证
        validate(valid, obj) {
            return this.$refs.form.validate(valid, obj)
        },
        scrollToField(prop) {
            return this.$refs.form.scrollToField(prop)
        },
        resetFields() {
            return this.$refs.form.resetFields()
        },
        //提交
        submit() {
            this.$refs.form.validate((valid, obj) => {
                if (valid) {
                    this.subloading = true
                    this.$emit('submit', this.form)
                } else {
                    this.$refs.form.scrollToField(Object.keys(obj)[0])
                    return false
                }
            })
        },
        //提交
        slotsubmit() {
            this.$refs.form.validate((valid, obj) => {
                if (valid) {
                    console.log('----提交3----')
                    this.subloading = true
                    this.$emit('slotsubmitemit', this.form)
                } else {
                    this.$refs.form.scrollToField(Object.keys(obj)[0])
                    return false
                }
            })
        },
        // 返回
        goBack() {
            this.$emit('goBack', this.form)
        },
        changebuttonloading() {
            this.subloading = false
        },
        // 处理formitems
        // handlerformitems(form) {
        //     this.filed.forEach((item) => {
        //         if (item.children) {
        //             for (let i in item.children) {
        //                 let itm = item.children[i]
        //                 if (itm.options.mergecontent) {
        //                     if (form[item.name] === itm.options.mergecontent) {
        //                         // console.log('数值一致消失', form[item.name], itm.options.mergecontent)
        //                         itm.options.merge = true
        //                         form[itm.name] = null
        //                     } else {
        //                         // console.log('数值不一致出现', form[item.name], itm.options.mergecontent)
        //                         itm.options.merge = false
        //                     }
        //                 } else if (itm.options.showcontent || itm.options.showcontent == 0) {
        //                     if (form[item.name] === itm.options.showcontent) {
        //                         // console.log('数值一致出现', form[item.name], itm.options.showcontent)
        //                         itm.options.merge = false
        //                     } else {
        //                         // console.log('数值不一致消失', form[item.name], itm.options.showcontent)
        //                         itm.options.merge = true
        //                         form[itm.name] = null
        //                     }
        //                 } else {
        //                     // console.log('------------------------')
        //                     // console.log(item)
        //                     if (form[item.name]) {
        //                         itm.options.merge = false
        //                     }
        //                 }
        //             }
        //         }
        //     })
        // },
        handlerformitems(form) {
            this.filed.forEach((item) => {
                if (item.children) {
                    for (let i in item.children) {
                        let itm = item.children[i]
                        this.cyclehandlerformitems(itm, form, item)
                    }
                }
            })
        },
        cyclehandlerformitems(itm, form, item) {
            if (itm.options.mergecontent || itm.options.mergecontent == 0) {
                if (form[item.name]) {
                    if (form[item.name] === itm.options.mergecontent || form[item.name].length == 0) {
                        // console.log('数值一致消失', form[item.name], itm.options.mergecontent)
                        itm.options.merge = true
                        form[itm.name] = null
                    } else {
                        // console.log('数值不一致出现', form[item.name], itm.options.mergecontent)
                        itm.options.merge = false
                    }
                }
            } else if (itm.options.showcontent || itm.options.showcontent == 0) {
                if (form[item.name] === itm.options.showcontent) {
                    // console.log('数值一致出现', form[item.name], itm.options.showcontent)
                    itm.options.merge = false
                } else {
                    // console.log('数值不一致消失', form[item.name], itm.options.showcontent)
                    itm.options.merge = true
                    form[itm.name] = null
                }
            } else {
                // console.log(form[item.name])
                // console.log(itm)
                if (form[item.name]) {
                    itm.options.merge = false
                }
            }
            if (itm.children) {
                itm.children.forEach((im) => {
                    this.cyclehandlerformitems(im, form, itm)
                })
            }
        },
        changeremoteitems(api) {
            this.$emit('changeremoteitems', api)
        },
        //处理动态隐藏 (暂时没用)
        hideHandle(item) {
            if (item.hideHandle) {
                const exp = eval(item.hideHandle.replace(/\$/g, 'this.form'))
                return exp
            }
            return false
        }
    }
}
</script>

<style>
.form-title {
    border-bottom: 1px solid #eee;
    margin-bottom: 20px;
    font-size: 17px;
    padding-bottom: 15px;
    color: #3c4a54;
    font-weight: bold;
}
.secondarr {
    position: relative;
    /* background: rgb(134, 134, 134); */
    width: 13px;
    height: 18px;
    top: 5px;
    border-left: 3px solid rgb(177, 177, 177);
    border-bottom: 3px solid rgb(177, 177, 177);
    /* left: 0.5px; */
    /* margin-right: 8px; */
    margin-right: 10px;
}
.userlist {
    width: 100%;
    height: 30px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
    font-size: 14px;
    padding: 1px 11px;
}
</style>
