<!--
 * @author: 风源
 * @name: 类名
 * @desc: 介绍
 * @LastEditTime: 2022-12-12 19:53:39
 * @FilePath: \eyc3_pc_base\src\views\report\date\day.vue
-->
<template>
	<yp_list
		ref="table"
		:url="url"
		row-key="id"
		:columns="columns"
		stripe
		:add="add"
		:derive="derive"
		:formitems="formitems"
		:paging="false"
	>
	</yp_list>
</template>

<script>
export default {
	name: "ReportDateDay",
	data() {
		return {
			url: "report/get_day_sum",
			columns: [
				{
					label: "汇总日期",
					prop: "date",
				},
				{
					label: "汇总金额",
					prop: "money",
				},

				{
					label: "真实金额",
					prop: "real_money",
				},
				{
					label: "虚拟金额",
					prop: "virtual_money",
				},
				{
					label: "补贴金额",
					prop: "subsidy_money",
				},				
			],
			derive: {
				filename:"每日汇总"
			},
			formitems: [
				{
					label: "汇总月份",
					name: "month",
					value: this.$TOOL.dateFormat(new Date(), "yyyy-MM"),
					component: "date",
					options: {
						type: "month",
						valueFormat: "YYYY-MM",
					},
				},
				{
					label: "餐厅",
					name: "dininghall_id",
					value: "",
					component: "select",
					options: {
						remote: {
							api: `dininghall/get_all`,
							data: { name: "b" },
							label: "title",
							value: "id",
						},
						items: [
							{
								label: "全部",
								value: "",
							},
						],
					},
				},
				{
					label: "选择人员",
					name: "userlst",
					value: "",
					component: "selectUser"
				},
				{
					label: "离职人员",
					name: "isleave",
					value: false,
					component: "switch",
				},				
			],
		};
	},
};
</script>

<style></style>
