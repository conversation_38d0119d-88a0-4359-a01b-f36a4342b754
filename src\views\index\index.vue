<template>
    <el-main>
        <el-row>
            <el-col :span="19">
                <div
                    class="list"
                    :style="{ height: `${boxHeight - 120}px` }"
                >
                    <div class="title">{{ year }}年{{ month }}月{{ day }}日 周{{ week }}</div>
                    <el-row v-if="this.overdueList.length != 0">
                        <el-col :span="24">
                            <div class="sec-title">逾期任务</div>
                        </el-col>
                        <template
                            v-for="(el, i) in overdueList"
                            :key="i"
                        >
                            <el-col :span="6">
                                <div
                                    class="card"
                                    @click="openDetail(el, 0)"
                                >
                                    <div class="task-title">
                                        <div class="tleft">
                                            <!-- <img
                                                :src="`/img/lv${el.priority}.png`"
                                                alt=""
                                            /> -->
                                            <span>{{ el.task_title }}</span>
                                        </div>
                                        <span class="tips">已逾期</span>
                                    </div>
                                    <div class="status item">
                                        <span class="left">任务状态</span>
                                        <span class="right">{{ statusList[el.status] }}</span>
                                    </div>
                                    <div class="time item">
                                        <span class="left">开始时间</span>
                                        <span class="right">{{ el.begin_time }}</span>
                                    </div>
                                    <div class="time item">
                                        <span class="left">结束时间</span>
                                        <span class="right">{{ el.end_time }}</span>
                                    </div>
                                </div>
                            </el-col>
                        </template>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <div class="sec-title">任务待办</div>
                        </el-col>
                        <template
                            v-for="(el, i) in noOverdueList"
                            :key="i"
                        >
                            <el-col :span="6">
                                <div
                                    class="card"
                                    @click="openDetail(el)"
                                >
                                    <div class="task-title">
                                        <div class="tleft">
                                            <!-- <img
                                                :src="`/img/lv${el.priority}.png`"
                                                alt=""
                                            /> -->
                                            <span>{{ el.task_title }}</span>
                                        </div>
                                    </div>
                                    <div class="status item">
                                        <span class="left">任务状态</span>
                                        <span class="right">{{ statusList[el.status] }}</span>
                                    </div>
                                    <div class="time item">
                                        <span class="left">开始时间</span>
                                        <span class="right">{{ el.begin_time ? el.begin_time : '未报时' }}</span>
                                    </div>
                                    <div class="time item">
                                        <span class="left">结束时间</span>
                                        <span class="right">{{ el.end_time ? el.end_time : '未报时' }}</span>
                                    </div>
                                </div>
                            </el-col>
                        </template>
                    </el-row>
                </div>
            </el-col>
            <el-col :span="5">
                <!-- 统计数据 -->
                <div class="statistics">
                    <div class="title">任务统计</div>
                    <el-row>
                        <el-col :span="8">
                            <div
                                class="item"
                                @click="goList(0)"
                            >
                                <div class="l1">待验收</div>
                                <div class="l2">{{ overview.need_confirm }}</div>
                            </div>
                        </el-col>
                        <el-col :span="8">
                            <div
                                class="item"
                                @click="goList(1)"
                            >
                                <div class="l1">待完成</div>
                                <div class="l2">{{ overview.need_finish }}</div>
                            </div>
                        </el-col>
                        <el-col :span="8">
                            <div
                                class="item"
                                @click="goList(2)"
                            >
                                <div class="l1">已完成</div>
                                <div class="l2">{{ overview.done }}</div>
                            </div>
                        </el-col>
                    </el-row>
                </div>
                <!-- 待验收 -->
                <div
                    class="await"
                    :style="{ height: `${boxHeight - 286}px` }"
                >
                    <div class="title">待验收</div>
                    <div
                        class="content"
                        v-if="this.checkList.length != 0"
                    >
                        <template
                            v-for="(el, i) in checkList"
                            :key="i"
                        >
                            <div
                                class="card"
                                @click="openDetail(el, 1)"
                            >
                                <div class="title">
                                    <!-- <img
                                        src="/img/lv1.png"
                                        alt=""
                                    /> -->
                                    {{ el.task_title }}
                                </div>
                                <div class="name user">人员：{{ el.user_name }}</div>
                                <div class="name">时间：{{ el.begin_time }}-{{ el.begin_time }}</div>
                            </div>
                        </template>
                    </div>
                    <el-empty
                        description="暂无内容"
                        v-else
                    />
                </div>
            </el-col>
        </el-row>
        <el-drawer
            title="任务详情"
            v-model="drawer"
            size="40%"
            class="el-drawer"
        >
            <el-container v-loading="loading">
                <el-main>
                    <div class="basics">
                        <div class="title">基础</div>
                        <el-divider />
                        <div class="btns">
                            <el-form
                                :model="infoForm"
                                label-width="120px"
                                label-position="top"
                            >
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="人员">
                                            {{ infoForm.node_info.user_name }} {{infoForm.node_info.title ? '_' + infoForm.node_info.title : ''  }}
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="任务分组">
                                            {{ infoForm.group_title }}
                                        </el-form-item>
                                    </el-col>
                                    <el-col
                                        :span="12"
                                        v-if="infoForm.parentnodes"
                                    >
                                        <el-form-item label="上一节点">
                                            {{ infoForm.parentnodes }}
                                        </el-form-item>
                                    </el-col>
                                    <el-col
                                        :span="12"
                                        v-if="infoForm.nextnodes"
                                    >
                                        <el-form-item label="下一节点">
                                            {{ infoForm.nextnodes }}
                                        </el-form-item>
                                    </el-col>
                                    <el-col
                                        :span="12"
                                        v-if="infoForm.progress"
                                    >
                                        <el-form-item label="任务进度"> {{ infoForm.progress }}% </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-form-item label="任务时间">
                                    <template v-if="infoForm.node_info.duration">
                                        {{ infoForm.node_info.begin_time }} -- {{ infoForm.node_info.end_time }}
                                    </template>
                                    <template v-else>
                                        <el-button @click="manHour">请填报工时</el-button>
                                    </template>
                                </el-form-item>
                            </el-form>
                        </div>
                    </div>
                    <div class="task">
                        <div class="title">任务内容</div>
                        <el-divider />
                        <div class="stitle">{{ infoForm.title }}</div>
                        <div class="desc">
                            {{ infoForm.content }}
                        </div>
                        <div
                            class="accessory"
                            v-if="infoForm.file"
                        >
                            <div class="stitle">任务附件</div>
                            <template
                                v-for="(el, i) in JSON.parse(infoForm.file)"
                                :key="i"
                            >
                                <div class="file">
                                    <!-- <img
                                        :src="el.url"
                                        alt=""
                                        @click="openFile(el.url)"
                                    /> -->
                                    <div
                                        class="name"
                                        @click="openFile(el.url)"
                                    >
                                        {{ el.name }}
                                    </div>
                                    <div
                                        class="download"
                                        @click="download(el)"
                                    >
                                        下载
                                    </div>
                                </div>
                            </template>
                        </div>
                        <div class="flow">
                            <div class="stitle">任务流程</div>
                            <el-timeline>
                                <el-timeline-item
                                    v-for="(activity, index) in infoForm.process"
                                    :key="index"
                                    :timestamp="activity.created_at"
                                >
                                    {{ activity.user_name }}{{ activity.action }}
                                </el-timeline-item>
                            </el-timeline>
                        </div>
                    </div>
                </el-main>
                <el-footer>
                    <termplate v-if="infoForm.status == 0">
                        <template v-if="infoForm.node_info.duration">
                            <el-button
                                type="primary"
                                round
                                icon="el-icon-edit-pen"
                                @click="completeTask"
                                >每日提报</el-button
                            >
                        </template>
                        <template v-else>
                            <el-button round>未开始</el-button>
                        </template>
                    </termplate>
                    <termplate v-if="infoForm.status == 1">
                        <template v-if="isCheck">
                            <el-button
                                type="primary"
                                round
                                icon="el-icon-check"
                                @click="check(0)"
                                >验收通过</el-button
                            >
                            <el-button
                                type="danger"
                                round
                                icon="el-icon-close"
                                @click="check(1)"
                                >未达标</el-button
                            >
                        </template>
                        <template v-else>
                            <el-button round>待验收</el-button>
                        </template>
                    </termplate>
                    <termplate v-if="infoForm.status == 2">
                        <el-button round>我已完成</el-button>
                    </termplate>
                </el-footer>
            </el-container>
        </el-drawer>
    </el-main>
</template>

<script>
import useTabs from '@/utils/useTabs'
import { ElMessage, ElNotification } from 'element-plus'
import scEcharts from '@/components/scEcharts'
export default {
    name: 'IndexIndex',
    components: {
        scEcharts
    },
    data() {
        return {
            // 年月日
            year: 0,
            month: 0,
            day: 0,
            weeks: ['日', '一', '二', '三', '四', '五', '六'],
            week: '',
            // 逾期任务列表
            overdueList: [],
            // 未逾期任务
            noOverdueList: [],
            // 待验收任务
            checkList: [],
            // 弹窗
            drawer: false,
            form: {
                name: '',
                region: '',
                date1: '',
                date2: '',
                delivery: false,
                type: [],
                resource: '',
                desc: ''
            },
            overview: {
                need_confirm: 0,
                need_finish: 0,
                done: 0
            },
            infoForm: {
                node_info: {}
            },
            statusList: ['待完成', '待验收', '已完成'],
            loading: false,
            isCheck: 0,
            messageInfo: {}
        }
    },
    created() {
        this.messageInfo = this.$TOOL.session.get('meaasgeInfo')
        // console.log('cuoleme',this.messageInfo)
        if (this.messageInfo) {
            this.openDetail({ task_id: this.messageInfo.id, id: this.messageInfo.node_id }, 0)
        }
    },
    mounted() {
        console.log('router1111', this.$route)
        const now = new Date()
        this.year = now.getFullYear()
        this.month = now.getMonth() + 1
        this.day = now.getDate()
        this.week = this.weeks[new Date().getDay()]
        this.getOverdueList()
        this.getNoOverdueList()
        this.getCheckList()
        this.getOverview()
    },
    computed: {
        boxHeight() {
            return document.documentElement.clientHeight
        }
    },
    methods: {
        // 获取预期任务列表
        getOverdueList() {
            this.$HTTP
                .get('task/get_my_all', { delay: 1 })
                .then((res) => {
                    // console.log('预期任务', res)
                    if (res.errcode == 0) {
                        this.overdueList = res.result
                    } else {
                        ElMessage.error(res.errmsg)
                    }
                })
                .finally(() => {})
        },
        // 获取未预期任务列表
        getNoOverdueList() {
            this.$HTTP
                .get('task/get_my_all', { delay: 0 })
                .then((res) => {
                    // console.log('未预期任务', res)
                    if (res.errcode == 0) {
                        this.noOverdueList = res.result
                    } else {
                        ElMessage.error(res.errmsg)
                    }
                })
                .finally(() => {})
        },
        // 获取预期任务列表
        getCheckList() {
            this.$HTTP
                .get('task/get_my_ls', { page: 1, per_page: 1000, type: 0 })
                .then((res) => {
                    // console.log('待验收任务', res)
                    if (res.errcode == 0) {
                        this.checkList = res.result.data
                    } else {
                        ElMessage.error(res.errmsg)
                    }
                })
                .finally(() => {})
        },
        // 获取预期任务列表
        getOverview() {
            this.$HTTP
                .get('task/get_overview')
                .then((res) => {
                    if (res.errcode == 0) {
                        this.overview = res.result
                    } else {
                        ElMessage.error(res.errmsg)
                    }
                })
                .finally(() => {})
        },
        // 点击打开详情
        openDetail(el, isCheck) {
            if (isCheck || isCheck == 0) {
                this.isCheck = isCheck
            }
            this.drawer = true
            // console.log('点击打开详情', el)
            this.$HTTP
                .get('task/get_my_info', { id: el.task_id, node_id: el.id })
                .then((res) => {
                    if (res.errcode == 0) {
                        this.infoForm = res.result
                        if (this.infoForm.parent_nodes.length != 0) {
                            let parentnodes = ''
                            this.infoForm.parent_nodes.forEach((el) => {
                                parentnodes = parentnodes + el.user_name + '-' + el.title + ' '
                            })
                            this.infoForm.parentnodes = parentnodes
                        }
                        if (this.infoForm.next_nodes.length != 0) {
                            let nextnodes = ''
                            console.log('this.infoForm.next_nodes', this.infoForm)
                            if(this.infoForm.type==0){
                                this.infoForm.next_nodes.forEach((el) => {
                                    nextnodes = nextnodes + el.user_name 
                                })
                            }else{
                                this.infoForm.next_nodes.forEach((el) => {
                                    nextnodes = nextnodes + el.user_name + '-' + el.title + ' '
                                })
                            }
                            this.infoForm.nextnodes = nextnodes
                        }
                        if (this.infodatas.file) {
                            this.infodatas.files = JSON.parse(this.infodatas.file)
                        }
                    } else {
                        ElMessage.error(res.errmsg)
                    }
                })
                .finally(() => {})
        },
        // 跳转列表
        goList(type) {
            this.$router.push({ path: '/index/list', query: { type } })
        },
        //报工时
        manHour() {
            this.$prompt('请填写该项目工时', '工作时长', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                inputPlaceholder: '请输入时长（天）'
            })
                .then(({ value }) => {
                    this.loading = true
                    this.$HTTP
                        .get('task/post_modify_time', {
                            id: this.infoForm.node_info.id,
                            task_id: this.infoForm.id,
                            duration: value
                        })
                        .then((res) => {
                            if (res.errcode == 0) {
                                ElMessage.success('提交成功')
                                this.drawer = false
                                this.getNoOverdueList()
                            } else {
                                ElMessage.error(res.errmsg)
                            }
                            this.loading = false
                        })
                        .finally(() => {})
                })
                .catch(() => {})
        },
        // 预览文件
        openFile(url) {
            window.open(url)
        },
        // 下载文件
        download(el) {
            this.$TOOL.runtime.biz.util.downloadFile({
                url: el.url, //要下载的文件的url
                name: el.name, //定义下载文件名字
                onProgress: function (msg) {
                    // 文件下载进度回调
                },
                onSuccess: function (result) {},
                onFail: function () {}
            })
        },
        // 完成任务
        completeTask() {
            this.loading = true
            this.$prompt('每日提报', '请填写当前任务完成百分比', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                inputPlaceholder: '%'
            })
                .then(({ value }) => {
                    // alert(value)
                    this.$HTTP
                        .post('task/post_complete', {
                            id: this.infoForm.node_info.id
                        })
                        .then((res) => {
                            if (res.errcode == 0) {
                                ElMessage.success('提交成功')
                                this.drawer = false
                                this.getOverdueList()
                                this.getNoOverdueList()
                            } else {
                                ElMessage.error(res.errmsg)
                            }
                            this.loading = false
                        })
                        .finally(() => {})
                })
                .catch(() => {})
        },
        // 验收任务
        check(type) {
            this.loading = true
            this.$HTTP
                .post('task/post_confirm', {
                    id: this.infoForm.id,
                    node_id: this.infoForm.node_info.id,
                    type
                })
                .then((res) => {
                    if (res.errcode == 0) {
                        ElMessage.success('提交成功')
                        this.drawer = false
                        this.getCheckList()
                    } else {
                        ElMessage.error(res.errmsg)
                    } 
                    this.loading = false
                })
                .finally(() => {})
        }
    }
}
</script>

<style  lang="scss" scoped>
.list {
    margin-right: 16px;
    padding: 20px 0 0 20px;
    background-color: #fff;
    width: calc(100%-16px);
    border-radius: 8px;

    .title {
        margin-bottom: 26px;
        font-size: 20px;
        font-weight: 550;
    }

    .sec-title {
        margin-bottom: 20px;
        font-size: 16px;
    }

    .card {
        margin-right: 20px;
        margin-bottom: 20px;
        padding: 20px;
        background-color: #f5f7fa;
        border-radius: 8px;

        .task-title {
            display: flex;
            justify-content: space-between;
            font-size: 14px;

            .tleft {
                display: flex;
                align-content: center;

                img {
                    margin-right: 8px;
                    width: 18px;
                    height: 18px;
                }
            }

            .tips {
                font-size: 12px;
                color: #ff5219;
            }
        }

        .item {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
            font-size: 12px;

            .left {
                color: #606266;
            }
        }
    }

    .card:hover {
        background-color: #ecf5ff;
        cursor: pointer;
    }
}

.statistics {
    width: 100%;
    padding: 20px;
    background-color: #fff;
    height: 150px;
    border-radius: 8px;

    .title {
        margin-bottom: 20px;
        font-size: 20px;
    }

    .item {
        text-align: center;
        cursor: pointer;

        .l1 {
            font-size: 13px;
            color: #747677;
            margin-bottom: 16px;
        }

        .l2 {
            font-size: 16px;
        }
    }
}

.await {
    margin-top: 16px;
    padding: 20px;
    background-color: #fff;
    width: 100%;
    border-radius: 8px;

    .title {
        margin-bottom: 20px;
        font-size: 20px;
    }

    .card {
        padding: 16px;
        margin-bottom: 20px;
        background-color: #f5f7fa;
        border-radius: 8px;

        .title {
            display: flex;
            align-content: center;
            margin-bottom: 8px;
            font-size: 14px;

            img {
                margin-right: 8px;
                width: 18px;
                height: 18px;
            }
        }

        .user {
            margin-bottom: 8px;
        }

        .name {
            color: #606266;
        }
    }

    .card:hover {
        background-color: #ecf5ff;
        cursor: pointer;
    }
}

.el-drawer {
    font-size: 14px;

    .basics {
        // padding: 0 20px;
        .title {
            // margin-bottom: 20px;
            font-size: 16px;
            font-weight: 550;
        }

        .btns {
            // margin-top: 30px;

            // .item {
            //     display: flex;
            //     padding: 9px 16px;
            //     margin-bottom: 12px;
            //     width: 40%;
            //     line-height: 16px;
            //     border-radius: 17px;
            //     border: 1px solid #eaebed;
            // }
        }
    }

    .task {
        .title {
            // margin-bottom: 20px;
            font-size: 16px;
            font-weight: 550;
        }

        .stitle {
            margin-bottom: 16px;
            font-size: 16px;
        }

        .accessory {
            margin-top: 30px;

            .file {
                position: relative;
                display: flex;
                align-content: center;
                margin-top: 12px;
                background-color: #ebf5ff;
                padding: 8px;
                width: 60%;
                line-height: 45px;
                border-radius: 8px;

                img {
                    margin-right: 8px;
                    width: 45px;
                    height: 45px;
                    border-radius: 4px;
                }

                .name {
                    width: 80%;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .download {
                    position: absolute;
                    right: 4px;
                    color: #409eff;
                    cursor: pointer;
                }
            }
        }

        .flow {
            margin-top: 20px;
        }
    }
}
</style>
