
<template>
    <div style="display: flex">
        <div>
            <div>
                <el-select
                    v-model="keywords"
                    filterable
                    remote
                    reserve-keyword
                    placeholder="请输入关键词"
                    :remote-method="remoteMethod"
                    :loading="loading"
                    :clearable="true"
                    size="mini"
                    @change="currentSelect"
                    style="width: 500px"
                >
                    <el-option
                        v-for="item in options"
                        :key="item.id"
                        :label="item.name"
                        :value="item"
                        class="one-text"
                    >
                        <span style="float: left">{{ item.name }}</span>
                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.district }}</span>
                    </el-option>
                </el-select>
            </div>
            <div
                id="container"
                class="container"
            ></div>
        </div>
    </div>
    <slot name="botton">
        <el-button
            style="margin-top: 10px"
            type="primary"
            @click="submiraddress"
            >确认</el-button
        >
    </slot>
</template>
   
  <script>
import AMapLoader from '@amap/amap-jsapi-loader'
import { objectPick } from '@vueuse/shared'
import { ElMessage, ElNotification } from 'element-plus'
window._AMapSecurityConfig = {
    securityJsCode: 'e5944c153662eb9392d0628ef3ea8721'
}
export default {
    name: 'TestIndex',
    props: {
        // 配置内容
        item: { type: Object, default: () => {} },
        // 表格信息
        form: { type: Object, default: () => {} }
    },
    emits: ['downdialog'],
    data() {
        return {
            // 地图实例
            map: null,
            // 标记点
            marker: '',
            // 地址逆解析
            geoCoder: null,
            // 行政区
            districtSearch: null,
            // 地址搜索
            placeSearch: null,
            // 搜索提示
            AutoComplete: null,
            // 搜索关键字
            keywords: '',
            // 位置信息
            siteform: {
                // lng: '',
                // lat: '',
                // address: '',
                // adcode: '' //地区编码
            },
            // 搜索节流阀
            loading: false,
            // 搜索提示信息
            options: [],
            // 省份编码
            pcode: [
                {
                    name: '安徽省',
                    code: '340000'
                },
                {
                    name: '北京市',
                    code: '110000'
                },
                {
                    name: '重庆市',
                    code: '500000'
                },
                {
                    name: '福建省',
                    code: '350000'
                },
                {
                    name: '广东省',
                    code: '440000'
                },
                {
                    name: '甘肃省',
                    code: '620000'
                },
                {
                    name: '广西壮族自治区',
                    code: '450000'
                },
                {
                    name: '河南省',
                    code: '410000'
                },
                {
                    name: '黑龙江省',
                    code: '230000'
                },
                {
                    name: '湖南省',
                    code: '430000'
                },
                {
                    name: '吉林省',
                    code: '220000'
                },
                {
                    name: '江苏省',
                    code: '320000'
                },
                {
                    name: '辽宁省',
                    code: '210000'
                },
                {
                    name: '宁夏回族自治区',
                    code: '640000'
                },
                {
                    name: '青海省',
                    code: '630000'
                },
                {
                    name: '山东省',
                    code: '370000'
                },
                {
                    name: '山西省',
                    code: '140000'
                },
                {
                    name: '陕西省',
                    code: '610000'
                },
                {
                    name: '天津市',
                    code: '120000'
                },
                {
                    name: '新疆维吾尔自治区',
                    code: '650000'
                },
                {
                    name: '云南省',
                    code: '530000'
                },
                {
                    name: '浙江省',
                    code: '330000'
                },
                {
                    name: '上海市',
                    code: '310000'
                },
                {
                    name: '西藏自治区',
                    code: '540000'
                },
                {
                    name: '江西省',
                    code: '360000'
                },
                {
                    name: '四川省',
                    code: '510000'
                },
                {
                    name: '海南藏族自治州',
                    code: '632500'
                },
                {
                    name: '河北省',
                    code: '130000'
                },
                {
                    name: '湖北省',
                    code: '420000'
                },
                {
                    name: '贵州省',
                    code: '520000'
                },
                {
                    name: '内蒙古自治区',
                    code: '150000'
                }
            ]
        }
    },
    mounted() {
        // 处理form 关联数据
        if (this.item.options) {
            if (this.item.options.relatedata) {
                for (let i in this.item.options.relatedata) {
                    this.siteform[i] = this.item.options.relatedata[i]
                }
            }
        }
        this.initMap()
    },
    methods: {
        initMap() {
            AMapLoader.load({
                // 你申请的Key
                key: 'dd7b7e94a28a14ea8f13b83d71f9c81b',
                version: '2.0',
                // 需要用到的插件
                plugins: ['AMap.Geocoder', 'AMap.AutoComplete', 'AMap.PlaceSearch', 'AMap.DistrictSearch']
            })
                .then((AMap) => {
                    this.map = new AMap.Map('container', {
                        viewMode: '3D', //是否为3D地图模式
                        zoom: 5, //初始化地图级别
                        center: [105.602725, 37.076636] //初始化地图中心点位置
                    })

                    //地址逆解析插件
                    this.geoCoder = new AMap.Geocoder({
                        city: '010', //城市设为北京，默认：“全国”
                        // city: _this.siteform.region_code
                        radius: 1000 //范围，默认：500
                    })

                    // 区域范围插件
                    this.districtSearch = new AMap.DistrictSearch({
                        // 创建行政区查询对象
                        extensions: 'all', // 返回行政区边界坐标等具体信息
                        level: 'province' // 设置查询行政区级别为 区
                    })

                    //
                    this.placeSearch = new AMap.PlaceSearch({})

                    // 搜索提示插件
                    this.AutoComplete = new AMap.AutoComplete({ city: '全国', citylimit: true })
                    if (this.form.longitude && this.form.latitude) {
                        // console.log('--------------324------------')
                        // console.log(this.form)
                        // 获取经纬度
                        this.siteform.longitude = this.form.longitude
                        this.siteform.latitude = this.form.latitude
                        // 清除点
                        this.removeMarker()
                        // 标记点
                        this.setMapMarker()
                    } else {
                        this.map.plugin('AMap.Geolocation', function () {
                            var geolocation = new AMap.Geolocation({
                                // 是否使用高精度定位，默认：true
                                enableHighAccuracy: true,
                                // 设置定位超时时间，默认：无穷大
                                timeout: 10000,
                                // 定位按钮的停靠位置的偏移量
                                offset: [10, 20],
                                //定位成功后将定位到的位置作为地图中心点，默认：true
                                panToLocation: true,
                                //  定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：false
                                zoomToAccuracy: true,
                                //  定位按钮的排放位置,  RB表示右下
                                position: 'RB'
                            })

                            geolocation.getCityInfo((status, result) => {
                                //只能获取当前用户所在城市和城市的经纬度
                                if (status == 'complete') {
                                    // console.log('result', result)
                                    onSuccess(result)
                                } else {
                                    ElNotification({
                                        title: '获取城市失败,请手动选择地址',
                                        type: 'danger'
                                    })
                                }
                            })
                        })
                        var _this = this
                        function onSuccess(data) {
                            _this.siteform.longitude = data.position[0]
                            _this.siteform.latitude = data.position[1]
                            // console.log(111, data)
                            // console.log(111, _this.siteform)
                            // 清除点
                            _this.removeMarker()
                            // 标记点
                            _this.setMapMarker()
                        }
                    }

                    //点击获取经纬度;
                    this.map.on('click', (e) => {
                        // 获取经纬度
                        this.siteform.longitude = e.lnglat.lng
                        this.siteform.latitude = e.lnglat.lat
                        // 清除点
                        this.removeMarker()
                        // 标记点
                        this.setMapMarker()
                    })
                })
                .catch((err) => {
                    // 错误
                    console.log(err)
                })
        },
        // 标记点
        setMapMarker() {
            // 自动适应显示想显示的范围区域
            this.map.setFitView()
            this.marker = new AMap.Marker({
                map: this.map,
                position: [this.siteform.longitude, this.siteform.latitude]
            })
            // 逆解析地址
            this.toGeoCoder()
            this.map.setFitView()
            this.map.add(this.marker)
        },
        // 清除点
        removeMarker() {
            if (this.marker) {
                this.map.remove(this.marker)
            }
        },
        // 逆解析地址
        toGeoCoder() {
            let lnglat = [this.siteform.longitude, this.siteform.latitude]
            this.geoCoder.getAddress(lnglat, (status, result) => {
                if (status === 'complete' && result.regeocode) {
                    var data = result.regeocode
                    // console.log('-----------------------------')
                    // console.log(data)
                    this.siteform.address = data.formattedAddress
                    this.siteform.city = data.addressComponent.city
                    this.siteform.city_code = data.addressComponent.citycode
                    this.siteform.province = data.addressComponent.province
                    this.pcode.forEach((item) => {
                        if (item.name == this.siteform.province) {
                            this.siteform.province_code = item.code
                        }
                    })
                    this.siteform.region = data.addressComponent.district
                        ? data.addressComponent.district
                        : data.addressComponent.township
                    this.siteform.region_code = data.addressComponent.adcode
                    var arrdetail = data.formattedAddress.split(this.siteform.region)
                    this.siteform.detail = arrdetail[1]
                }
            })
        },
        // 省份位置
        districtSearchFn() {
            console.log(this.siteform)
            var _that = this
            this.districtSearch.search(this.siteform.detail, function (status, result) {
                var bounds = result.districtList[0].boundaries
                var tembound = bounds[0]
                var longitude = (tembound[0].lng + tembound[tembound.length - 1].lng) / 2
                var latitude = (tembound[0].lat + tembound[tembound.length - 1].lat) / 2
                // // 标记点
                // _that.setMapMarker()
                // _that.removeMarker()
                // _that.map.setFitView()
                _that.map.setZoom(7)
                _that.map.setCenter([longitude, latitude])
                // 清除点
                _that.removeMarker()
            })
        },
        // //
        // placeSearchFn() {
        //     this.placeSearch.setCity(this.siteform.detail)
        //     // this.placeSearch.search(e.name)
        // },
        // 搜索
        remoteMethod(query) {
            // console.log(query)
            if (query !== '') {
                this.loading = true
                setTimeout(() => {
                    this.loading = false
                    this.AutoComplete.search(query, (status, result) => {
                        // console.log(result)
                        this.options = result.tips
                    })
                }, 200)
            } else {
                this.options = []
            }
        },
        // 选中提示
        currentSelect(val) {
            // console.log('--------511--------------')
            // console.log(val)
            // 清空时不执行后面代码
            if (!val) {
                return
            }
            this.siteform = {
                longitude: val.location ? val.location.lng : '',
                latitude: val.location ? val.location.lat : '',
                address: val.district + val.address,
                adcode: val.adcode,
                detail: val.location ? '' : val.name
            }
            this.keywords = val.name
            if (val.location) {
                // 清除点
                this.removeMarker()
                // 标记点
                this.setMapMarker()
            } else {
                // this.placeSearchFn()
                this.districtSearchFn()
            }
        },
        // 确定按钮
        submiraddress() {
            var arrformat = this.item.options.format.split('+')
            this.form[this.item.name] = ''
            if (this.siteform.latitude) {
                arrformat.forEach((item) => {
                    this.form[this.item.name] = this.form[this.item.name] + this.siteform[item]
                })
                var postData = {}
                var rs = this.item.options.relatedata
                for (const key in rs) {
                    if (rs[key].substring(0, 1) == '$') {
                        postData[key] = this.siteform[rs[key].substring(1)]
                        // if (postData[key] == '') return
                    } else {
                        postData[key] = rs[key]
                    }
                }
                Object.assign(this.form, postData)
                if (postData.city.length == 0) {
                    // console.log(324324)
                    this.form.city = ''
                }
                // console.log('-----------------------511-----------1---------------')
                // console.log(this.form)
                this.$emit('downdialog', true)
            } else {
                // console.log('-----------------------511--------------2------------')
                // console.log(this.form)
                // console.log(this.siteform)
                this.form[this.item.name] = this.siteform.address
                var postData = {}
                var rs = this.item.options.relatedata
                for (const key in rs) {
                    if (rs[key].substring(0, 1) == '$') {
                        postData[key] = this.siteform[rs[key].substring(1)]
                        // if (postData[key] == '') return
                    } else {
                        postData[key] = rs[key]
                    }
                }
                Object.assign(this.form, postData)
                this.$emit('downdialog', true)
            }
        }
    }
}
</script>
   
  <style>
.container {
    margin-top: 20px;
    width: 500px;
    height: 300px;
}
</style>