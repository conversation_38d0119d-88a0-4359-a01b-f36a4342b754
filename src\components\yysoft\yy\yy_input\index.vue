<!--
 * @Descripttion: 文件导出
 * @version: 1.1
 * @Author: sakuya
 * @Date: 2022年5月24日16:20:12
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-12-07 11:43:10
-->

<template>
    <el-form-item label="222">
        <el-input />
    </el-form-item>
</template>

<script>
import columnSet from './column'

export default {
    name: 'yy_input',
    components: {
        columnSet
    },
    props: {
        set: { type: Object, default: () => {} },
        apiObj: { type: Object, default: () => {} },
        fileName: { type: String, default: '' },
        fileTypes: { type: Array, default: () => ['xlsx'] },
        data: { type: Object, default: () => {} },
        showData: { type: Boolean, default: false },
        async: { type: Boolean, default: false },
        column: { type: Array, default: () => [] },
        blob: { type: <PERSON>olean, default: false },
        progress: { type: <PERSON><PERSON><PERSON>, default: true }
    },
    data() {
        return {
            dialog: false,
            formData: {
                fileName: this.fileName,
                fileType: this.fileTypes[0]
            },
            columnData: [],
            downLoading: false,
            downLoadProgress: 0,
            asyncLoading: false
        }
    },
    watch: {
        'formData.fileType'(val) {
            if (this.formData.fileName.includes('.')) {
                this.formData.fileName =
                    this.formData.fileName.substring(0, this.formData.fileName.lastIndexOf('.')) + '.' + val
            } else {
                this.formData.fileName = this.formData.fileName + '.' + val
            }
        }
    },
    mounted() {},
    methods: {
        open() {
            this.dialog = true
            this.formData = {
                fileName: (this.fileName ? this.fileName : new Date().getTime() + '') + '.' + this.fileTypes[0],
                fileType: this.fileTypes[0]
            }
            this.columnData = JSON.parse(JSON.stringify(this.column))
        },
        close() {
            this.dialog = false
        },
        toQueryString(obj) {
            let arr = []
            for (var k in obj) {
                arr.push(`${k}=${obj[k]}`)
            }
            return (arr.length > 0 ? '?' : '') + arr.join('&')
        }
    }
}
</script>

<style>
</style>
