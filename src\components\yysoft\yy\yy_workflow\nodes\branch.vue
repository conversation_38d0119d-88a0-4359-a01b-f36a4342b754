<template>
    <div class="branch-wrap">
        <div class="branch-box-wrap">
            <div class="branch-box">
                <el-button
                    class="add-branch"
                    type="success"
                    plain
                    round
                    @click="addTerm"
                    >添加分支</el-button
                >
                <div
                    class="col-box"
                    v-for="(item, index) in nodeConfig.conditionNodes"
                    :key="index"
                >
                    <div class="condition-node">
                        <div class="condition-node-box">
                            <div
                                class="auto-judge"
                                @click="show(index)"
                            >
                                <div
                                    class="sort-left"
                                    v-if="index != 0"
                                    @click.stop="arrTransfer(index, -1)"
                                >
                                    <el-icon><el-icon-arrow-left /></el-icon>
                                </div>
                                <div class="title">
                                    <span class="node-title">{{ item.nodeName }}</span>
                                    <!-- 排序 -->
                                    <!-- <span class="priority-title">{{ item.priorityLevel }}</span> -->
                                    <el-icon
                                        class="close"
                                        @click.stop="delTerm(index)"
                                        ><el-icon-close
                                    /></el-icon>
                                </div>
                                <div class="content">
                                    <span v-if="toText(nodeConfig, index)">{{ toText(nodeConfig, index) }}</span>
                                    <span
                                        v-else
                                        class="placeholder"
                                        >请设置</span
                                    >
                                </div>
                                <div
                                    class="sort-right"
                                    v-if="index != nodeConfig.conditionNodes.length - 1"
                                    @click.stop="arrTransfer(index)"
                                >
                                    <el-icon><el-icon-arrow-right /></el-icon>
                                </div>
                            </div>
                            <add-node v-model="item.childNode"></add-node>
                        </div>
                    </div>
                    <slot
                        v-if="item.childNode"
                        :node="item"
                    ></slot>
                    <div
                        class="top-left-cover-line"
                        v-if="index == 0"
                    ></div>
                    <div
                        class="bottom-left-cover-line"
                        v-if="index == 0"
                    ></div>
                    <div
                        class="top-right-cover-line"
                        v-if="index == nodeConfig.conditionNodes.length - 1"
                    ></div>
                    <div
                        class="bottom-right-cover-line"
                        v-if="index == nodeConfig.conditionNodes.length - 1"
                    ></div>
                </div>
            </div>
            <add-node v-model="nodeConfig.childNode"></add-node>
        </div>
        <el-drawer
            title="条件设置"
            v-model="drawer"
            destroy-on-close
            append-to-body
            :size="600"
        >
            <template #header>
                <div class="node-wrap-drawer__title">
                    <label
                        @click="editTitle"
                        v-if="!isEditTitle"
                        >{{ form.nodeName }}<el-icon class="node-wrap-drawer__title-edit"><el-icon-edit /></el-icon
                    ></label>
                    <el-input
                        v-if="isEditTitle"
                        ref="nodeTitle"
                        v-model="form.nodeName"
                        clearable
                        @blur="saveTitle"
                        @keyup.enter="saveTitle"
                    ></el-input>
                </div>
            </template>
            <el-container>
                <el-main style="padding: 0 20px 20px 20px">
                    <el-form label-position="top">
                        <el-form-item label="人员安排">
                            <el-radio-group
                                v-model="form.uType"
                                :model="form.uType"
                            >
                                <el-radio :label="0">人员</el-radio>
                                <el-radio :label="1">岗位</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item
                            label="选择任务节点的人员"
                            v-if="form.uType == '0'"
                        >
                            <yy_selectuser
                                v-model="form.userLst"
                                :model="form.userLst"
                                :isRecommend="isRecommend"
                                :recommendOptions.sync="recommendOption"
                                :items="{ type: 'edit' }"
                            ></yy_selectuser>
                        </el-form-item>
                        <el-form-item
                            v-if="form.uType == '1'"
                            label="岗位名称"
                            prop="unit_title"
                        >
                            <el-select
                                placeholder="请选择"
                                style="min-width: 420px"
                                v-model="form.post"
                                filterable
                                value-key="id"
                                @change="getUnit"
                            >
                                <el-option
                                    v-for="item in unitIds"
                                    :key="item.id"
                                    :label="item.title"
                                    :value="item"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item
                            v-if="form.uType == '1'"
                            label="岗位人数"
                            prop="unit_title"
                        >
                            <el-input-number
                                v-model="form.num"
                                :min="1"
                            ></el-input-number>
                        </el-form-item>
                    </el-form>
                </el-main>
                <el-footer>
                    <el-button
                        type="primary"
                        @click="save"
                        >保存</el-button
                    >
                    <el-button @click="drawer = false">取消</el-button>
                </el-footer>
            </el-container>
        </el-drawer>
    </div>
</template>

<script>
import addNode from './addNode'

export default {
    props: {
        modelValue: { type: Object, default: () => {} },
        isRecommend: { type: Boolean, default: false },
        recommendOptions: { type: Object, default: () => {} },
        postItems: { type: Array, default: () => [] }
    },
    components: {
        addNode
    },
    data() {
        return {
            nodeConfig: {},
            drawer: false,
            isEditTitle: false,
            index: 0,
            form: {
                uType: 0,
                num: 1
            },
            unitIds: this.postItems,
            tableData: [
                {
                    date: '2016-05-02',
                    name: '王小虎',
                    address: '上海市普陀区金沙江路 1518 弄'
                },
                {
                    date: '2016-05-04',
                    name: '王小虎',
                    address: '上海市普陀区金沙江路 1517 弄'
                },
                {
                    date: '2016-05-01',
                    name: '王小虎',
                    address: '上海市普陀区金沙江路 1519 弄'
                },
                {
                    date: '2016-05-03',
                    name: '王小虎',
                    address: '上海市普陀区金沙江路 1516 弄'
                }
            ],
            recommendOption: {}
        }
    },
    watch: {
        modelValue() {
            this.nodeConfig = this.modelValue
        }
    },
    mounted() {
        this.nodeConfig = this.modelValue
        this.recommendOption = this.recommendOptions
    },
    methods: {
        getUnit() {},
        show(index) {
            this.index = index
            this.form = {}
            this.form = JSON.parse(JSON.stringify(this.nodeConfig.conditionNodes[index]))
            this.drawer = true
            // console.log('6666', this.nodeConfig.conditionNodes[index].nodeName)
            if (this.isRecommend) {
                let name = this.nodeConfig.conditionNodes[index].nodeName
                this.recommendOption.post.node_title = name
            }
        },
        editTitle() {
            this.isEditTitle = true
            this.$nextTick(() => {
                this.$refs.nodeTitle.focus()
            })
        },
        saveTitle() {
            this.isEditTitle = false
            console.log('11111111', this.form)
            if (this.isRecommend) {
                this.recommendOption.post.node_title = this.form.nodeName
            }
        },
        createStaticArray(num, id) {
            const validNum = Math.max(0, parseInt(num, 10) || 0)
            return Array(validNum).fill(id)
        },
        save() {
            if (this.form.uType == 0) {
                delete this.form.num
                delete this.form.post
                delete this.form.postionId
                delete this.form.postionName
                if (this.form.position) {
                    delete this.form.position
                }
            } else if (this.form.uType == 1) {
                delete this.form.userLst
                this.form.position = this.createStaticArray(this.form.num, this.form.postionId)
            }
            this.nodeConfig.conditionNodes[this.index] = this.form
            this.$emit('update:modelValue', this.nodeConfig)
            this.drawer = false
        },
        addTerm() {
            let len = this.nodeConfig.conditionNodes.length + 1
            this.nodeConfig.conditionNodes.push({
                nodeName: '分支' + len,
                type: 3,
                priorityLevel: len
                // conditionMode: 1,
                // conditionList: []
            })
        },
        delTerm(index) {
            this.nodeConfig.conditionNodes.splice(index, 1)
            if (this.nodeConfig.conditionNodes.length == 1) {
                if (this.nodeConfig.childNode) {
                    if (this.nodeConfig.conditionNodes[0].childNode) {
                        this.reData(this.nodeConfig.conditionNodes[0].childNode, this.nodeConfig.childNode)
                    } else {
                        this.nodeConfig.conditionNodes[0].childNode = this.nodeConfig.childNode
                    }
                }
                this.$emit('update:modelValue', this.nodeConfig.conditionNodes[0].childNode)
            }
        },
        reData(data, addData) {
            if (!data.childNode) {
                data.childNode = addData
            } else {
                this.reData(data.childNode, addData)
            }
        },
        arrTransfer(index, type = 1) {
            this.nodeConfig.conditionNodes[index] = this.nodeConfig.conditionNodes.splice(
                index + type,
                1,
                this.nodeConfig.conditionNodes[index]
            )[0]
            this.nodeConfig.conditionNodes.map((item, index) => {
                item.priorityLevel = index + 1
            })
            this.$emit('update:modelValue', this.nodeConfig)
        },
        addConditionList() {
            this.form.conditionList.push({
                label: '',
                field: '',
                operator: '=',
                value: ''
            })
        },
        deleteConditionList(index) {
            this.form.conditionList.splice(index, 1)
        },
        getUnit(item) {
            this.form.postionId = item.id
            this.form.postionName = item.title
        },
        toText(nodeConfig, index) {
            // var { conditionList } = nodeConfig.conditionNodes[index]
            // if (conditionList && conditionList.length == 1) {
            //     const text = conditionList.map((item) => `${item.label}${item.operator}${item.value}`).join(' 和 ')
            //     return text
            // } else if (conditionList && conditionList.length > 1) {
            //     const conditionModeText = nodeConfig.conditionNodes[index].conditionMode == 1 ? '且行' : '或行'
            //     return conditionList.length + '个条件，' + conditionModeText
            // }
            // } else {
            //     if (index == nodeConfig.conditionNodes.length - 1) {
            //         return '其他条件进入此流程'
            //     } else {
            //         return false
            //     }
            // }

            // console.log('分支节点展示文字 conditionList-->', nodeConfig, index)
            var conditionList = nodeConfig.conditionNodes[index]
            // console.log('分支节点展示文字 conditionList11-->', conditionList)
            if (conditionList.uType) {
                return conditionList.num + '个' + conditionList.postionName
            } else {
                if (conditionList.userLst && conditionList.userLst.length > 0) {
                    let users = ''
                    let userList = JSON.parse(conditionList.userLst).users
                    for (let i in userList) {
                        if (i == userList.length - 1) {
                            users = users + userList[i].name
                        } else {
                            users = users + userList[i].name + '、'
                        }
                    }
                    // console.log('测试 --->', userList, users)
                    return users
                } else {
                    // if (nodeConfig.userSelectFlag) {
                    return '请选择'
                    // } else {
                    //     return false
                    // }
                }
            }
        }
    }
}
</script>

<style>
</style>
