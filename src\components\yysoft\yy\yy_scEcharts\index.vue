<template>
    <el-row :gutter="15">
        <el-col>
            <el-card shadow="never">
                <scEcharts
                    height="300px"
                    :option="option"
                ></scEcharts>
            </el-card>
        </el-col>
    </el-row>
</template>

<script>
import scEcharts from '@/components/scEcharts'

/**
 * 引入组件 @/components/scEcharts
 * 组件内部会自动加载主题 @/components/scEcharts/echarts-theme-T.js
 * 支持props包括 height，width，option
 * 组件export百度Echarts所有方法，使用方式: new scEcharts[fun]
 */

export default {
    name: 'yy_scEcharts',
    components: {
        scEcharts
    },
    props: {
        title: { type: Object, default: () => {} }, //标题文本
        grid: { type: Object, default: () => {} }, //用于决定直角坐标系内绘图网格的位置
        typeEcharts: { type: String, default: '' }, //"bar"基础柱状图、"line"基础折线图
        barWidth: { type: String, default: '15px' },
        label: { type: Boolean, default: false },
        radius: { type: Array, default: () => ['40%', '70%'] },
        center: { type: Array, default: () => ['50%', '60%'] },
        anchor: { type: Object, default: () => {} },
        xAxis: { type: Object, default: () => {} },
        yAxis: { type: Object, default: () => {} },
        tooltip: { type: Object, default: () => {} },
        radar: { type: Object, default: () => {} },
        series: { type: Object, default: () => {} }
    },
    mounted() {},
    data() {
        return {
            option: {
                //标题文本
                title: this.title,
                //用于决定直角坐标系内绘图网格的位置
                grid: this.grid,
                // 可视化工具
                tooltip: this.tooltip,
                // x轴
                xAxis: this.xAxis,
                //y轴
                yAxis: this.yAxis,

                radar: this.radar,
                // series数据展示数组格式
                // series下面数据里的type是表格显示得类型
                series: this.series
            }
        }
    }
}
</script>

<style></style>
