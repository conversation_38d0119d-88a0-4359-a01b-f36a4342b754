<template>
    <el-button
        :type="buttonType"
        :icon="`el-icon-${icon}`"
        @click="openDrawer"
    >
        {{ label }}
    </el-button>

    <el-drawer
        class="el-drawer"
        v-model="drawer"
        append-to-body
        :title="title"
        :size="`${width}%`"
        :before-close="handleClose"
    >
        <slot name="content"></slot>
    </el-drawer>
</template>

<script>
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
export default {
    name: '',
    props: {
        label: { type: String, default: '新增' },
        width: { type: Number, default: '60' },
        buttonType: { type: String, default: 'primary' },
        icon: { type: String, default: 'Plus' },
        title: { type: String, default: '' }
    },
    data() {
        return {
            drawer: false
        }
    },
    mounted() {},
    methods: {
        openDrawer() {
            this.drawer = true
        }
    }
}
</script>

<style scoped lang="scss">
.el-drawer {
    width: 100%;
    .el-descriptions {
        padding: 0 10px;
        // width: 80%;
    }
}
</style>