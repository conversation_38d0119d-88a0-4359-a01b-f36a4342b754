<template>
    <el-container v-loading="loading">
        <el-header>
            <h2>修改</h2>
            <el-button @click="exportJson">提交</el-button>
        </el-header>
        <el-main>
            <yy_workflow v-model="data.nodeConfig"></yy_workflow>
        </el-main>
    </el-container>
</template>

<script>
import { ElMessage } from 'element-plus'
import useTabs from '@/utils/useTabs'
export default {
    name: 'workflow',
    data() {
        return {
            data: {
                // id: 1,
                name: '任务流',
                nodeConfig: {
                    nodeName: '任务开启',
                    type: 0,
                    nodeRoleList: [],
                    childNode: {}
                }
            },
            id: 0,
            loading: false
        }
    },
    mounted() {
        this.id = JSON.parse(this.$route.params.remote).data.id
        console.log(this.$route.params, this.id)
        this.getForm()
    },
    methods: {
        exportJson() {
            // this.$message('返回值请查看F12控制台console.log()')
            // console.log(this.data)
            let process = JSON.parse(JSON.stringify(this.data))
            this.handleJson(process.nodeConfig.childNode)
            this.$HTTP
                .post('flow/post_modify', {
                    id: this.id,
                    title: this.data.nodeConfig.title,
                    process: JSON.stringify(process)
                })
                .then((res) => {
                    console.log('获取结果 --->', res)
                    if (res.errcode == 0) {
                        // console.log('获取任务分组1 --->', res.result)
                        // this.taskGroupList = res.result
                        ElMessage.success('提交成功')
                        useTabs.close()
                    } else {
                        ElMessage.error(res.errmsg)
                    }
                })
                .finally(() => {})
        },
        getForm() {
            this.loading = true
            this.$HTTP
                .get('flow/get_info', { id: this.id })
                .then((res) => {
                    console.log('任务流详情', res)
                    let process = JSON.parse(res.result.process)
                    this.handleJsonStr(process.nodeConfig.childNode)
                    // console.log('2222222222222222222222')
                    this.data = process
                })
                .finally(() => {
                    this.loading = false
                })
        },
        // 处理json
        handleJson(obj) {
            if (obj.userLst && typeof obj.userLst === 'string') {
                try {
                    obj.userLst = JSON.parse(obj.userLst);
                } catch (error) {
                    console.error('解析userLst出错:', error);
                    obj.userLst = [];
                }
            }
            for (let i in obj) {
                if (i == 'conditionNodes') {
                    obj[i].forEach((el) => {
                        if (el.userLst && typeof el.userLst === 'string') {
                            try {
                                el.userLst = JSON.parse(el.userLst);
                            } catch (error) {
                                console.error('解析conditionNodes中的userLst出错:', error);
                                el.userLst = [];
                            }
                        }
                    });
                } else if (i == 'childNode') {
                    if (obj[i] && obj[i].type) {
                        if (obj[i].userLst && typeof obj[i].userLst === 'string') {
                            try {
                                obj[i].userLst = JSON.parse(obj[i].userLst);
                            } catch (error) {
                                console.error('解析childNode中的userLst出错:', error);
                                obj[i].userLst = [];
                            }
                        }
                        if (obj[i].childNode && obj[i].childNode.type) {
                            this.handleJson(obj[i].childNode); // 修正递归调用，传递子节点而不是当前节点
                        }
                    }
                }
            }
        },
        // 处理jsonStr
        handleJsonStr(obj) {
            if (obj.userLst) {
                obj.userLst = JSON.stringify(obj.userLst)
            }
            for (let i in obj) {
                // console.log('22222222222222222', i)
                if (i == 'conditionNodes') {
                    obj[i].forEach((el) => {
                        el.userLst = JSON.stringify(el.userLst)
                    })
                } else if (i == 'childNode') {
                    if (obj[i].type) {
                        obj[i].userLst = JSON.stringify(obj[i].userLst)
                        if (obj[i].childNode.type) {
                            this.handleJsonStr(obj)
                        }
                    }
                }
            }
        }
    }
}
</script>

<style></style>
