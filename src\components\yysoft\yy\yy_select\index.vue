<!--
 * @Descripttion: 文件导出
 * @version: 1.1
 * @Author: sakuya
 * @Date: 2022年5月24日16:20:12
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-12-09 14:26:09
-->

<template>
    <el-select
        v-model="value"
        :multiple="multiple"
        :placeholder="placeholder"
        clearable
        filterable
        style="width: 100%"
        @visible-change="getData"
        :loading="loading"
    >
        <el-option
            v-for="option in items"
            :key="option.value"
            :label="option.label"
            :value="option.value"
        >
            <img
                v-if="option.img"
                :src="option.img"
                style="width: 50px"
            />
            <span
                v-if="option.img"
                style="text-indent: 2em; display: inline-block"
                >{{ option.label }}</span
            >
        </el-option>
        <el-option
            v-for="option in remoteitms"
            :key="option[remote.value]"
            :label="option[remote.label]"
            :value="option[remote.value]"
        ></el-option>
        <el-option
            v-for="option in remoteitems"
            :key="option[remote.value]"
            :label="option[remote.label]"
            :value="option[remote.value]"
        ></el-option>
    </el-select>
</template>

<script>
export default {
    name: 'yy_select',
    props: {
        model: { type: String, default: '' },
        form: {
            type: Object,
            default: () => {
                return {}
            }
        },
        multiple: { type: Boolean, default: false },
        placeholder: { type: String, default: '' },
        remote: { type: Object, default: () => null },
        items: { type: Object, default: () => null },
        remoteitems: { type: Array, default: () => null },
        relatedata: { type: Object, default: () => null }
    },
    emits: ['changeremoteitems'],
    data() {
        return {
            remoteitms: [],
            loading: false,
            value: this.value,
            label: this.label,
            postData: {},
            selectitem: {},
            edit: {},
            islocal: false,
            isshow: true
        }
    },
    watch: {
        modelValue(val) {
            this.value = val
        },
        value(val) {
            if (this.remote) {
                // if (this.islocal) {
                if (this.remote.relatedata) {
                    var items = []
                    if (this.remoteitms.length >= 1) {
                        items = JSON.parse(JSON.stringify(this.remoteitms))
                    } else {
                        items = JSON.parse(JSON.stringify(this.remoteitems))
                    }
                    items.forEach((item) => {
                        if (item[this.remote.value] == val) {
                            // console.log(item)
                            var postData = {}
                            var rs = this.remote.relatedata
                            for (const key in rs) {
                                if (rs[key].substring(0, 1) == '$') {
                                    postData[key] = item[rs[key].substring(1)]
                                    // console.log(postData[key])
                                    // if (postData[key] == '') return
                                } else {
                                    postData[key] = rs[key]
                                }
                            }
                            Object.assign(this.form, postData)
                        }
                    })
                } else {
                    this.$emit('update:modelValue', val)
                }
                // }
            } else {
                if (this.relatedata) {
                    this.items.forEach((item) => {
                        if (item.value == val) {
                            // console.log(item)
                            var postData = {}
                            var rs = this.relatedata
                            for (const key in rs) {
                                if (rs[key].substring(0, 1) == '$') {
                                    postData[key] = item[rs[key].substring(1)]
                                    // console.log(postData[key])
                                    // if (postData[key] == '') return
                                } else {
                                    postData[key] = rs[key]
                                }
                            }
                            Object.assign(this.form, postData)
                        }
                    })
                }
            }
        }
    },
    mounted() {},
    methods: {
        //处理远程选项数据
        getData(e) {
            if (e) {
                if (this.remote) {
                    if (this.remote.isclickrefresh) {
                        this.loading = true
                        var postData = {}
                        var rs = this.remote.data
                        for (const key in rs) {
                            if (rs[key].substring(0, 1) == '$') {
                                postData[key] = this.form[rs[key].substring(1)]
                                // console.log(postData[key])
                                if (postData[key] == '') return
                            } else {
                                postData[key] = rs[key]
                            }
                        }
                        this.$HTTP.get(this.remote.api, postData).then((res) => {
                            if (res.errcode != 0) {
                                ElMessage.error(res.errmsg)
                            } else {
                                console.log(this.remote)
                                this.$emit('changeremoteitems', this.remote.api)
                                this.remoteitms = res.result
                                this.loading = false
                            }
                        })
                    } else if (!this.remoteitems) {
                        this.loading = true
                        var postData = {}
                        var rs = this.remote.data
                        for (const key in rs) {
                            if (rs[key].substring(0, 1) == '$') {
                                postData[key] = this.form[rs[key].substring(1)]
                                // console.log(postData[key])
                                if (postData[key] == '') return
                            } else {
                                postData[key] = rs[key]
                            }
                        }
                        this.$HTTP.get(this.remote.api, postData).then((res) => {
                            if (res.errcode != 0) {
                                ElMessage.error(res.errmsg)
                            } else {
                                if (this.remote.getresult) {
                                    if (this.remote.getresult == 'data_norepeat') {
                                        var getdata = res.result.data
                                        var postremoteitms = []
                                        var temporary = []
                                        getdata.forEach((item) => {
                                            let f = temporary.includes(item[this.remote.value])
                                            if (!f) {
                                                postremoteitms.push(item)
                                                temporary.push(item[this.remote.value])
                                            }
                                        })
                                        this.remoteitms = postremoteitms
                                    }
                                } else {
                                    this.remoteitms = Array.isArray(res.result) ? res.result : res.result.data
                                    // 为每一项增加配置
                                    if (this.remote.addobj) {
                                        this.remoteitms.forEach((el) => {
                                            this.remote.addobj.forEach((k) => {
                                                el[k.key] = k.value
                                            })
                                        })
                                    }
                                    // 单独增加一项的值
                                    if (this.remote.addItems) {
                                        this.remoteitms.unshift(...this.remote.addItems)
                                    }
                                }
                                this.islocal = true
                                this.loading = false
                            }
                        })
                    }
                }
            }
        }
    }
}
</script>

<style></style>
