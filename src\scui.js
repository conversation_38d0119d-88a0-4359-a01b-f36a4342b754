/*
 * @author: 风源
 * @name: 类名
 * @desc: 介绍
 * @LastEditTime: 2023-01-05 16:55:34
 * @FilePath: \eyc3_canyin_pc\src\scui.js
 */
import config from "./config"
import api from './api'
import tool from './utils/tool'
import http from "./utils/request"
import { permission, rolePermission } from './utils/permission'

import scTable from './components/scTable'
import scTableColumn from './components/scTable/column.js'
import scFilterBar from './components/scFilterBar'
import scUpload from './components/scUpload'
import scUploadMultiple from './components/scUpload/multiple'
import scUploadFile from './components/scUpload/file'
import scFormTable from './components/scFormTable'
import scTableSelect from './components/scTableSelect'
import scPageHeader from './components/scPageHeader'
import scSelect from './components/scSelect'
import scDialog from './components/scDialog'
import scForm from './components/scForm'
import scTitle from './components/scTitle'
import scWaterMark from './components/scWaterMark'
import scQrCode from './components/scQrCode'

import scStatusIndicator from './components/scMini/scStatusIndicator'
import scTrend from './components/scMini/scTrend'

import yp_list from '@/components/yysoft/yp/yp_list'
import yp_form from '@/components/yysoft/yp/yp_form'
import yp_list_tree from '@/components/yysoft/yp/yp_list_tree'
import yy_form from '@/components/yysoft/yy/yy_form'
import yy_queryform from '@/components/yysoft/yy/yy_queryform'
import yy_export from '@/components/yysoft/yy/yy_export'
import yy_upload from '@/components/yysoft/yy/yy_upload'
import yy_select from '@/components/yysoft/yy/yy_select'
import yy_selectuser from '@/components/yysoft/yy/yy_selectuser'
import yy_button from '@/components/yysoft/yy/yy_button'
import yy_dialog from '@/components/yysoft/yy/yy_dialog'
import yy_inputtap from '@/components/yysoft/yy/yy_inputtap'
import yy_checkbox from '@/components/yysoft/yy/yy_checkbox'
import yy_cascader from '@/components/yysoft/yy/yy_cascader'
import yy_tab from '@/components/yysoft/yy/yy_tab'
import yy_descriptions from '@/components/yysoft/yy/yy_descriptions'
import yy_checkboxDialog from '@/components/yysoft/yy/yy_checkboxDialog'
import yy_workflow from '@/components/yysoft/yy/yy_workflow'
import yy_popup from '@/components/yysoft/yy/yy_popup'


import auth from './directives/auth'
import role from './directives/role'
import time from './directives/time'
import copy from './directives/copy'
import errorHandler from './utils/errorHandler'

import * as elIcons from '@element-plus/icons-vue'
import * as scIcons from './assets/icons'


export default {
	install(app) {
		//挂载全局对象
		app.config.globalProperties.$CONFIG = config;
		app.config.globalProperties.$TOOL = tool;
		app.config.globalProperties.$HTTP = http;
		app.config.globalProperties.$API = api;
		app.config.globalProperties.$AUTH = permission;
		app.config.globalProperties.$ROLE = rolePermission;

		//注册全局组件
		app.component('scTable', scTable);
		app.component('scTableColumn', scTableColumn);
		app.component('scFilterBar', scFilterBar);
		app.component('scUpload', scUpload);
		app.component('scUploadMultiple', scUploadMultiple);
		app.component('scUploadFile', scUploadFile);
		app.component('scFormTable', scFormTable);
		app.component('scTableSelect', scTableSelect);
		app.component('scPageHeader', scPageHeader);
		app.component('scSelect', scSelect);
		app.component('scDialog', scDialog);
		app.component('scForm', scForm);
		app.component('scTitle', scTitle);
		app.component('scWaterMark', scWaterMark);
		app.component('scQrCode', scQrCode);
		app.component('scStatusIndicator', scStatusIndicator);
		app.component('scTrend', scTrend);

		app.component('yp_list', yp_list);
		app.component('yp_form', yp_form);
		app.component('yp_list_tree', yp_list_tree);
		app.component('yy_form', yy_form);
		app.component('yy_queryform', yy_queryform);
		app.component('yy_export', yy_export);
		app.component('yy_upload', yy_upload);
		app.component('yy_select', yy_select);
		app.component('yy_selectuser', yy_selectuser);
		app.component('yy_button', yy_button);
		app.component('yy_dialog', yy_dialog);
		app.component('yy_inputtap', yy_inputtap);
		app.component('yy_checkbox', yy_checkbox);
		app.component('yy_cascader', yy_cascader);
		app.component('yy_tab', yy_tab);
		app.component('yy_descriptions', yy_descriptions);
		app.component('yy_checkboxDialog', yy_checkboxDialog);
		app.component('yy_workflow', yy_workflow);
		app.component('yy_popup', yy_popup);

		//注册全局指令
		app.directive('auth', auth)
		app.directive('role', role)
		app.directive('time', time)
		app.directive('copy', copy)

		//统一注册el-icon图标
		for (let icon in elIcons) {
			app.component(`ElIcon${icon}`, elIcons[icon])
		}
		//统一注册sc-icon图标
		for (let icon in scIcons) {
			app.component(`ScIcon${icon}`, scIcons[icon])
		}
		tool.data.remove("TOKEN")

		//关闭async-validator全局控制台警告
		window.ASYNC_VALIDATOR_NO_WARNING = 1

		//全局代码错误捕捉
		app.config.errorHandler = errorHandler

		if (config.RUN_TEST) {
			tool.data.set("TOKEN", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJjb3JwX3Byb2R1Y3QiOiJjYW55aW4iLCJ0eXBlcyI6ImlzdiIsImNvcnBpZCI6ImRpbmdiOTYxNGRmOTQzNDJmNTcwYTEzMjBkY2IyNWU5MTM1MSIsImNvcnBfbmFtZSI6Ilx1NGUwMFx1NGUwMFx1NzlkMVx1NjI4MFx1NTE4NVx1OTBlOFx1NWYwMFx1NTNkMVx1NWU3M1x1NTNmMCIsInVzZXJpZCI6IjMzMzYzNTMzMjEyNDIxMDY2NCIsIm5hbWUiOiJcdTVmMjBcdTYzMmZcdTUzMTciLCJzdGFmZl9uYW1lIjoiXHU1ZjIwXHU2MzJmXHU1MzE3Iiwic3RhZmZpZCI6IjMzMzYzNTMzMjEyNDIxMDY2NCIsImRpbmluZ2hhbGxfaWQiOjk0LCJkaW5pbmdoYWxsX3RpdGxlIjoiXHU5ZWQ4XHU4YmE0XHU5OTEwXHU1Mzg1In0.f74WsBdmgIh6eMqye6xBjJDY-k--JVF29inG4S2CAqI");
		}

	},
}
