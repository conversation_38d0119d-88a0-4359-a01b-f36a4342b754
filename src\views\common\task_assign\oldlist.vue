<template>
    <el-container>
        <el-header>
            <el-button @click="exportJson">111</el-button>
        </el-header>
        <el-main>
            <yy_workflow v-model="data.nodeConfig"></yy_workflow>
        </el-main>
    </el-container>
</template>

<script>
export default {
    name: 'workflow',
    data() {
        return {
            data: {
                // id: 1,
                name: '任务流',
                nodeConfig: {
                    nodeName: '任务开启',
                    type: 0,
                    nodeRoleList: [],
                    childNode: {}
                }
            }
        }
    },
    mounted() {},
    methods: {
        exportJson() {
            this.$message('返回值请查看F12控制台console.log()')
            console.log(this.data)
        }
    }
}
</script>

<style></style>
